"use client";

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer, <PERSON><PERSON><PERSON>, Legend } from "recharts";
import { Server } from "lucide-react";

interface WordPressCategoryData {
  category: string;
  count: number;
}

interface WordPressCategoryChartProps {
  data: WordPressCategoryData[];
  totalWordPressDomains: number;
}

// Kolory dla wykresu kołowego
const COLORS = [
  '#3b82f6', // blue-500
  '#10b981', // emerald-500
  '#f59e0b', // amber-500
  '#ef4444', // red-500
  '#8b5cf6', // violet-500
  '#06b6d4', // cyan-500
  '#84cc16', // lime-500
  '#f97316', // orange-500
  '#ec4899', // pink-500
  '#6366f1', // indigo-500
  '#14b8a6', // teal-500
  '#a855f7', // purple-500
];

interface CustomTooltipProps {
  active?: boolean;
  payload?: Array<{
    value: number;
    payload: WordPressCategoryData;
  }>;
}

const CustomTooltip = ({ active, payload }: CustomTooltipProps) => {
  if (active && payload && payload.length) {
    const data = payload[0].payload;
    const total = payload[0].payload.count;

    return (
      <div className="bg-background border border-border rounded-lg p-3 shadow-lg">
        <p className="font-medium text-foreground">{data.category}</p>
        <p className="text-blue-600">
          <span className="font-semibold">{data.count}</span> domen WordPress
        </p>
      </div>
    );
  }
  return null;
};

const renderLabel = (entry: WordPressCategoryData & { percent: number }) => {
  return entry.percent > 5 ? `${entry.percent.toFixed(0)}%` : '';
};

export default function WordPressCategoryChart({ data, totalWordPressDomains }: WordPressCategoryChartProps) {
  // Filtruj i sortuj dane - pokaż tylko top 10 kategorii
  const sortedData = data
    .sort((a, b) => b.count - a.count)
    .slice(0, 10);

  // Dodaj procenty
  const dataWithPercent = sortedData.map(item => ({
    ...item,
    percent: (item.count / totalWordPressDomains) * 100
  }));

  if (data.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Server className="h-5 w-5" />
            WordPress - rozkład kategorii
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-muted-foreground">
            <Server className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>Brak domen WordPress do wyświetlenia</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Server className="h-5 w-5" />
          WordPress - rozkład kategorii ({totalWordPressDomains} domen)
        </CardTitle>
        <p className="text-sm text-muted-foreground">
          Top 10 kategorii z największą liczbą domen WordPress
        </p>
      </CardHeader>
      <CardContent>
        {/* Dodatkowe informacje */}
        <div className="space-y-2">
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-muted-foreground">Największa kategoria:</span>
              <p className="font-medium">
                {sortedData[0]?.category} ({sortedData[0]?.count} domen)
              </p>
            </div>
            <div>
              <span className="text-muted-foreground">Pokazano kategorii:</span>
              <p className="font-medium">
                {Math.min(10, data.length)} z {data.length}
              </p>
            </div>
          </div>
        </div>
        <div className="w-full">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={dataWithPercent}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={renderLabel}
                outerRadius={120}
                fill="#8884d8"
                dataKey="count"
                nameKey="category"
              >
                {dataWithPercent.map((entry, index) => (
                  <Cell
                    key={`cell-${index}`}
                    fill={COLORS[index % COLORS.length]}
                  />
                ))}
              </Pie>
              <Tooltip content={<CustomTooltip />} />
              <Legend
                verticalAlign="bottom"
                height={36}
                formatter={(value, entry) => (
                  <span className="text-sm">
                    {value} ({entry.payload?.count || 0})
                  </span>
                )}
              />
            </PieChart>
          </ResponsiveContainer>
        </div>

      </CardContent>
    </Card>
  );
}