"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { toast } from "sonner";
import {
  ArrowLeft,
  Shield,
  ShieldAlert,
  Calendar,
  ExternalLink,
  User,
  Globe,
  Database,
  AlertTriangle,
  CheckCircle,
  Clock,
  Info,
} from "lucide-react";

import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";

interface Plugin {
  name: string;
  installed_version: string;
  latest_version: string;
  status: string;
  source: string;
  source_links: string[];
  vulnerabilities_count: number;
  vulnerabilities: Vulnerability[];
}

interface Vulnerability {
  id: string;
  title: string;
  description: string;
  cvss_score?: number;
  cwe_id?: string;
  published?: string;
  affected_versions: Record<string, unknown>;
  patched_versions: Record<string, unknown>;
}

interface WordPressVersion {
  version_number: string;
  version_status: string;
  release_date?: string;
}

interface User {
  id: number;
}

interface ScanData {
  domain_id: string;
  domain_url: string;
  wordpress_version: WordPressVersion;
  plugins: Record<string, Plugin>;
  users: Record<string, User>;
  timestamp?: string;
}

interface Domain {
  id: string;
  domain: string;
  fullDomain: string;
  category: string;
  status: string;
}

interface ScanPageProps {
  params: Promise<{ id: string }>;
}

export default function ScanPage({ params }: ScanPageProps) {
  const router = useRouter();
  const [domain, setDomain] = useState<Domain | null>(null);
  const [scanData, setScanData] = useState<ScanData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [resolvedParams, setResolvedParams] = useState<{ id: string } | null>(
    null
  );

  // Resolve params
  useEffect(() => {
    params.then(setResolvedParams);
  }, [params]);

  // Fetch domain details and scan data
  useEffect(() => {
    if (!resolvedParams?.id) return;

    const fetchData = async () => {
      setIsLoading(true);
      try {
        // Fetch basic domain details
        const domainResponse = await fetch(`/api/domains/${resolvedParams.id}`);

        if (!domainResponse.ok) {
          if (domainResponse.status === 404) {
            toast.error("Domena nie została znaleziona");
            router.push("/dashboard/domains");
            return;
          }
          throw new Error("Błąd pobierania szczegółów domeny");
        }

        const domainData = await domainResponse.json();
        setDomain(domainData.domain);

        // Fetch scan data
        const scanResponse = await fetch(`/api/domains/${resolvedParams.id}/scan`);
        if (scanResponse.ok) {
          const scanResult = await scanResponse.json();
          if (scanResult.success) {
            setScanData(scanResult.data);
          }
        }
      } catch (error) {
        console.error("Błąd pobierania danych:", error);
        toast.error("Wystąpił błąd podczas pobierania danych");
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [resolvedParams, router]);

  const formatDate = (dateString?: string) => {
    if (!dateString) return "Nieznana data";
    return new Date(dateString).toLocaleDateString("pl-PL", {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const getCVSSClass = (score?: number) => {
    if (!score) return "text-muted-foreground";
    if (score >= 7.0) return "text-red-600";
    if (score >= 4.0) return "text-orange-600";
    return "text-yellow-600";
  };

  const getCVSSLabel = (score?: number) => {
    if (!score) return "Nieznana";
    if (score >= 7.0) return "Wysoka";
    if (score >= 4.0) return "Średnia";
    return "Niska";
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!domain) {
    return (
      <div className="text-center py-8">
        <p>Domena nie została znaleziona</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" asChild>
            <Link href={`/dashboard/domains/${domain.id}`}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Powrót do domeny
            </Link>
          </Button>
          <div className="flex gap-2">
            <h1 className="text-2xl font-bold flex items-center gap-2">
              <Shield className="h-6 w-6" />
              Wyniki Skanu Bezpieczeństwa
            </h1>
            <div className="text-muted-foreground flex items-center">
              {domain.domain} •{" "}
              <Badge variant="secondary">{domain.category}</Badge>
            </div>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button
            onClick={() => window.open(domain.fullDomain, "_blank")}
            variant="outline"
            size="sm"
          >
            <ExternalLink className="h-4 w-4 mr-2" />
            Otwórz domenę
          </Button>
        </div>
      </div>

      {!scanData ? (
        <Card>
          <CardContent className="text-center py-8">
            <Shield className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <h3 className="text-lg font-medium mb-2">Brak danych skanu</h3>
            <p className="text-muted-foreground">
              Nie znaleziono wyników skanu dla tej domeny. Wykonaj skan bezpieczeństwa, aby zobaczyć wyniki.
            </p>
          </CardContent>
        </Card>
      ) : (
        <>
          {/* Summary Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Łączna liczba pluginów
                </CardTitle>
                <Database className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {Object.keys(scanData.plugins).length}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Pluginy z podatnościami
                </CardTitle>
                <ShieldAlert className="h-4 w-4 text-red-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-red-600">
                  {Object.values(scanData.plugins).filter(p => p.vulnerabilities_count > 0).length}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Łączne podatności
                </CardTitle>
                <AlertTriangle className="h-4 w-4 text-orange-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-orange-600">
                  {Object.values(scanData.plugins).reduce((sum, p) => sum + p.vulnerabilities_count, 0)}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Nieaktualne pluginy
                </CardTitle>
                <Clock className="h-4 w-4 text-yellow-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-yellow-600">
                  {Object.values(scanData.plugins).filter(p => p.status === "Wymaga aktualizacji").length}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* WordPress Version Info */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Globe className="h-5 w-5" />
                Informacje o WordPress
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <p className="text-sm text-muted-foreground">Wersja WordPress</p>
                  <p className="font-medium">{scanData.wordpress_version.version_number}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Status wersji</p>
                  <Badge variant={scanData.wordpress_version.version_status === 'latest' ? 'default' : 'secondary'}>
                    {scanData.wordpress_version.version_status}
                  </Badge>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Data wydania</p>
                  <p className="font-medium">{formatDate(scanData.wordpress_version.release_date)}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Users Info */}
          {Object.keys(scanData.users).length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="h-5 w-5" />
                  Wykryte użytkownicy ({Object.keys(scanData.users).length})
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {Object.entries(scanData.users).map(([username, user]) => (
                    <div key={username} className="flex items-center justify-between p-3 border rounded-lg">
                      <span className="font-medium">{username}</span>
                      <Badge variant="outline">ID: {user.id}</Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Plugins List */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Database className="h-5 w-5" />
                Pluginy WordPress
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="">
                <div className="space-y-4">
                  {Object.entries(scanData.plugins).map(([slug, plugin]) => (
                    <div key={slug} className="border rounded-lg p-4">
                      <div className="flex items-start justify-between mb-3">
                        <div>
                          <h3 className="font-semibold text-lg">{plugin.name}</h3>
                          <p className="text-sm text-muted-foreground">Slug: {slug}</p>
                        </div>
                        <div className="flex items-center gap-2">
                          {plugin.vulnerabilities_count > 0 ? (
                            <Badge variant="destructive" className="flex items-center gap-1">
                              <ShieldAlert className="h-3 w-3" />
                              {plugin.vulnerabilities_count} podatność{plugin.vulnerabilities_count > 1 ? 'i' : ''}
                            </Badge>
                          ) : (
                            <Badge variant="default" className="flex items-center gap-1">
                              <CheckCircle className="h-3 w-3" />
                              Bezpieczny
                            </Badge>
                          )}
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-3">
                        <div>
                          <p className="text-sm text-muted-foreground">Zainstalowana wersja</p>
                          <p className="font-medium">{plugin.installed_version}</p>
                        </div>
                        <div>
                          <p className="text-sm text-muted-foreground">Najnowsza wersja</p>
                          <p className="font-medium">{plugin.latest_version}</p>
                        </div>
                        <div>
                          <p className="text-sm text-muted-foreground">Status</p>
                          <Badge variant={plugin.status === "Aktualny" ? "default" : "secondary"}>
                            {plugin.status}
                          </Badge>
                        </div>
                      </div>

                      {plugin.source && (
                        <div className="mb-3">
                          <p className="text-sm text-muted-foreground">Źródło detekcji</p>
                          <p className="text-sm">{plugin.source}</p>
                        </div>
                      )}

                      {plugin.source_links && plugin.source_links.length > 0 && (
                        <div className="mb-3">
                          <p className="text-sm text-muted-foreground">Linki źródłowe</p>
                          <div className="space-y-1">
                            {plugin.source_links.map((link, index) => (
                              <p key={index} className="text-xs font-mono text-muted-foreground break-all">

                                <a className="hover:text-white hover:underline" href={link} target="_blank">{link}</a>
                              </p>
                            ))}
                          </div>
                        </div>
                      )}

                      {plugin.vulnerabilities && plugin.vulnerabilities.length > 0 && (
                        <div className="mt-4">
                          <div className="border-b mb-4" />
                          <h4 className="font-medium mb-3 flex items-center gap-2">
                            <AlertTriangle className="h-4 w-4 text-red-500" />
                            Podatności ({plugin.vulnerabilities.length})
                          </h4>
                          <div className="space-y-3">
                            {plugin.vulnerabilities.map((vuln) => (
                              <div key={vuln.id} className="bg-muted/50 rounded-lg p-3">
                                <div className="flex items-start justify-between mb-2">
                                  <h5 className="font-medium text-sm">{vuln.title}</h5>
                                  {vuln.cvss_score && (
                                    <Badge
                                      variant="outline"
                                      className={`${getCVSSClass(vuln.cvss_score)} border-current`}
                                    >
                                      CVSS: {vuln.cvss_score} ({getCVSSLabel(vuln.cvss_score)})
                                    </Badge>
                                  )}
                                </div>

                                <div className="flex items-center gap-4 text-xs text-muted-foreground mb-2">
                                  <span>ID: <a className="hover:text-white hover:underline" href={`https://www.wordfence.com/threat-intel/vulnerabilities/id/${vuln.id}?source=cve`} target="_blank">{vuln.id}</a></span>
                                  {vuln.cwe_id && <span>CWE: {vuln.cwe_id}</span>}
                                  {vuln.published && (
                                    <span className="flex items-center gap-1">
                                      <Calendar className="h-3 w-3" />
                                      {formatDate(vuln.published)}
                                    </span>
                                  )}
                                </div>

                                {vuln.description && vuln.description !== 'Brak opisu' && (
                                  <p className="text-sm text-muted-foreground">{vuln.description}</p>
                                )}

                                {(Object.keys(vuln.affected_versions || {}).length > 0 ||
                                  Object.keys(vuln.patched_versions || {}).length > 0) && (
                                  <div className="mt-2 grid grid-cols-1 md:grid-cols-2 gap-3 text-xs">
                                    {Object.keys(vuln.affected_versions || {}).length > 0 && (
                                      <div>
                                        <p className="font-medium">Dotknięte wersje:</p>
                                        <p className="text-muted-foreground">
                                          {Object.keys(vuln.affected_versions).join(', ')}
                                        </p>
                                      </div>
                                    )}
                                    {Object.keys(vuln.patched_versions || {}).length > 0 && (
                                      <div>
                                        <p className="font-medium">Poprawione wersje:</p>
                                        <p className="text-muted-foreground">
                                          {Object.keys(vuln.patched_versions).join(', ')}
                                        </p>
                                      </div>
                                    )}
                                  </div>
                                )}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Scan Info */}
          {scanData.timestamp && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Info className="h-5 w-5" />
                  Informacje o skanie
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-muted-foreground">Data przeprowadzenia skanu</p>
                    <p className="font-medium">{formatDate(scanData.timestamp)}</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">URL domeny</p>
                    <p className="font-medium break-all">{scanData.domain_url}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </>
      )}
    </div>
  );
}