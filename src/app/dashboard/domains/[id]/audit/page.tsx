"use client";

import { useState, useEffect, useRef } from "react";
import { useRout<PERSON> } from "next/navigation";
import Link from "next/link";
import { toast } from "sonner";
import {
  ArrowLeft,
  Save,
  Mail,
  FileText,
  Send,
  Eye,
  ExternalLink,
  Sparkles,
  Code,
} from "lucide-react";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";

import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>eader,
  Al<PERSON><PERSON><PERSON>og<PERSON>itle,
} from "@/components/ui/alert-dialog";

interface Domain {
  id: string;
  domain: string;
  fullDomain: string;
  category: string;
  status: string;
  auditContent?: string;
  contact_data?: {
    contact_email?: string;
    contact_companyName?: string;
    contact_contactPerson?: string;
  };
}

interface AuditPageProps {
  params: Promise<{ id: string }>;
}

export default function AuditPage({ params }: AuditPageProps) {
  const router = useRouter();
  const [domain, setDomain] = useState<Domain | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [auditContent, setAuditContent] = useState("");
  const [isSaving, setIsSaving] = useState(false);
  const [isSendingEmail, setIsSendingEmail] = useState(false);
  const [showAiModal, setShowAiModal] = useState(false);
  const [aiPrompt, setAiPrompt] = useState("");
  const [isProcessingAi, setIsProcessingAi] = useState(false);
  const [showEmailConfirmDialog, setShowEmailConfirmDialog] = useState(false);
  const [testEmailAddress, setTestEmailAddress] = useState("");
  const [showFullHtmlPreview, setShowFullHtmlPreview] = useState(true);
  const iframeRef = useRef<HTMLIFrameElement>(null);

  // Email form state
  const [emailData, setEmailData] = useState({
    recipientEmail: "",
    subject: "",
  });

  const [resolvedParams, setResolvedParams] = useState<{ id: string } | null>(
    null
  );

  // Helper function to decode JSON string if needed
  const decodeAuditContent = (content: string): string => {
    if (!content) return "";

    try {
      // Try to parse as JSON - if it's a JSON string, decode it
      const parsed = JSON.parse(content);
      if (typeof parsed === "string") {
        return parsed;
      }
      // If it's not a string after parsing, return original
      return content;
    } catch (error) {
      console.log(error)
      // If JSON.parse fails, it's not a JSON string, return as is
      return content;
    }
  };

  // Helper function to get first email from comma-separated list
  const getFirstEmail = (emailString: string): string => {
    return emailString.split(',')[0].trim();
  };

  // Generate full HTML email content with styles
  const generateFullEmailHtml = (auditContent: string, domainName: string): string => {
    const decodedContent = decodeAuditContent(auditContent);

    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Audyt Bezpieczeństwa - ${domainName}</title>
        <style>
          body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333333;
            background-color: #f9f9f9;
            margin: 0;
            padding: 0;
          }
          .container {
            max-width: 700px;
            margin: 20px auto;
            padding: 25px;
            background-color: #ffffff;
            border: 1px solid #dddddd;
            border-radius: 5px;
          }
          h1, h2, h3, h4 {
            margin-top: 0;
            color: #222222;
          }
          h1 {
            font-size: 24px;
            border-bottom: 2px solid #eeeeee;
            padding-bottom: 10px;
            margin-bottom: 20px;
          }
          h2 {
            font-size: 20px;
            color: #c0392b;
            margin-top: 30px;
          }
          h3 {
            font-size: 16px;
            margin-top: 25px;
            color: #34495e;
          }
          table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
          }
          th, td {
            border: 1px solid #dddddd;
            padding: 12px;
            text-align: left;
          }
          th {
            background-color: #f2f2f2;
            font-weight: bold;
          }
          .status-update {
            font-weight: bold;
            color: #e67e22;
          }
          .status-ok {
            font-weight: bold;
            color: #27ae60;
          }
          .vulnerability {
1            margin-top: 15px;
          }
          .vulnerability-title {
            font-weight: bold;
            color: #c0392b;
          }
          .final-note {
            margin-top: 30px;
            padding: 20px;
            background-color: #eaf2f8;
            border-left: 4px solid #3498db;
          }
          strong.warning {
            color: #c0392b;
          }
          code {
            background-color: #ecf0f1;
            padding: 2px 5px;
            border-radius: 3px;
            font-family: monospace;
          }
        </style>
      </head>
      <body>
        <div class="content">
          <div style="width: 960px; max-width: 100%;">
            ${decodedContent}
          </div>
        </div>
      </body>
      </html>
    `;
  };

  // Update iframe with full HTML content
  const updateIframeContent = () => {
    if (iframeRef.current && auditContent && domain) {
      const fullHtml = generateFullEmailHtml(auditContent, domain.domain);
      const iframeDoc = iframeRef.current.contentDocument;
      if (iframeDoc) {
        iframeDoc.open();
        iframeDoc.write(fullHtml);
        iframeDoc.close();
      }
    }
  };

  // Update iframe when content changes and full preview is enabled
  useEffect(() => {
    if (showFullHtmlPreview) {
      updateIframeContent();
    }
  }, [auditContent, showFullHtmlPreview, domain]);

  // Fetch test email from settings
  const fetchTestEmail = async () => {
    try {
      const response = await fetch("/api/settings/test-email");
      if (response.ok) {
        const data = await response.json();
        setTestEmailAddress(data.testEmail || "");
      }
    } catch (_error) { // Renamed error to _error
      console.error("Błąd pobierania testowego emaila:", _error);
    }
  };

  // Resolve params
  useEffect(() => {
    params.then(setResolvedParams);
  }, [params]);

  // Fetch test email on component mount
  useEffect(() => {
    fetchTestEmail();
  }, []);

  // Fetch domain details
  useEffect(() => {
    if (!resolvedParams?.id) return;

    const fetchDomainDetails = async () => {
      setIsLoading(true);
      try {
        // Fetch basic domain details
        const domainResponse = await fetch(`/api/domains/${resolvedParams.id}`);

        if (!domainResponse.ok) {
          if (domainResponse.status === 404) {
            toast.error("Domena nie została znaleziona");
            router.push("/dashboard/domains");
            return;
          }
          throw new Error("Błąd pobierania szczegółów domeny");
        }

        const domainData = await domainResponse.json();
        setDomain(domainData.domain);

        // Fetch audit content separately
        const auditResponse = await fetch(`/api/domains/${resolvedParams.id}/audit-content`);
        if (auditResponse.ok) {
          const auditData = await auditResponse.json();
          if (auditData.success) {
            setAuditContent(auditData.auditContent || "");
          }
        }

        // Pre-populate email form - extract first email if multiple are provided
        const contactEmail = domainData.domain.contact_data?.contact_email || "";
        const firstEmail = contactEmail ? getFirstEmail(contactEmail) : "";

        setEmailData({
          recipientEmail: firstEmail,
          subject: `Audyt bezpieczeństwa - ${domainData.domain.domain}`,
        });
      } catch (error) {
        console.error("Błąd pobierania szczegółów domeny:", error);
        toast.error("Wystąpił błąd podczas pobierania szczegółów domeny");
      } finally {
        setIsLoading(false);
      }
    };

    fetchDomainDetails();
  }, [resolvedParams, router]);

  // Save audit content
  const saveAuditContent = async () => {
    if (!resolvedParams?.id) return;

    setIsSaving(true);
    try {
      const response = await fetch(`/api/domains/${resolvedParams.id}/audit`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          content: auditContent,
        }),
      });

      if (!response.ok) {
        throw new Error("Błąd zapisywania treści audytu");
      }

      const data = await response.json();
      if (data.success) {
        toast.success("Treść audytu została zapisana pomyślnie");
        // Update domain state
        if (domain) {
          setDomain({ ...domain, auditContent });
        }
      } else {
        throw new Error(data.error || "Błąd zapisywania treści audytu");
      }
    } catch (error) {
      console.error("Błąd zapisywania treści audytu:", error);
      toast.error("Wystąpił błąd podczas zapisywania treści audytu");
    } finally {
      setIsSaving(false);
    }
  };

  // Send test email to configured test address
  const sendTestEmail = async () => {
    if (!resolvedParams?.id) return;

    if (!auditContent.trim()) {
      toast.error("Brak treści audytu do wysłania");
      return;
    }

    if (!testEmailAddress.trim()) {
      toast.error("Brak skonfigurowanego adresu testowego. Skonfiguruj go w ustawieniach.");
      return;
    }

    setIsSendingEmail(true);
    try {
      const response = await fetch(
        `/api/domains/${resolvedParams.id}/send-audit-email`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            recipientEmail: testEmailAddress,
            subject: `[TEST] Audyt bezpieczeństwa - ${domain?.domain}`,
          }),
        }
      );

      const data = await response.json();

      if (data.success) {
        toast.success(`Testowy email z audytem został wysłany na ${testEmailAddress}`);
      } else {
        throw new Error(data.error || "Błąd wysyłania testowego emaila");
      }
    } catch (error) {
      console.error("Błąd wysyłania testowego emaila:", error);
      toast.error("Wystąpił błąd podczas wysyłania testowego emaila");
    } finally {
      setIsSendingEmail(false);
    }
  };

  // Send audit email
  const sendAuditEmail = async () => {
    if (!resolvedParams?.id) return;

    if (!emailData.recipientEmail.trim()) {
      toast.error("Adres email odbiorcy jest wymagany");
      return;
    }

    if (!emailData.subject.trim()) {
      toast.error("Temat wiadomości jest wymagany");
      return;
    }

    if (!auditContent.trim()) {
      toast.error("Brak treści audytu do wysłania");
      return;
    }

    // Extract first email address if multiple are provided (separated by comma)
    const firstEmail = getFirstEmail(emailData.recipientEmail);

    setIsSendingEmail(true);
    try {
      const response = await fetch(
        `/api/domains/${resolvedParams.id}/send-audit-email`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            recipientEmail: firstEmail,
            subject: emailData.subject,
          }),
        }
      );

      const data = await response.json();

      if (data.success) {
        const hasMultipleEmails = emailData.recipientEmail.includes(',');
        const successMessage = hasMultipleEmails
          ? `Email z audytem został wysłany na ${firstEmail} (pierwszy z podanych adresów)`
          : "Email z audytem został wysłany pomyślnie";
        toast.success(successMessage);
        setShowEmailConfirmDialog(false);
      } else {
        throw new Error(data.error || "Błąd wysyłania emaila");
      }
    } catch (error) {
      console.error("Błąd wysyłania emaila:", error);
      toast.error("Wystąpił błąd podczas wysyłania emaila");
    } finally {
      setIsSendingEmail(false);
    }
  };

  // Process content with AI
  const processWithAi = async () => {
    if (!aiPrompt.trim()) {
      toast.error("Wprowadź prompt dla AI");
      return;
    }

    setIsProcessingAi(true);
    try {
      const response = await fetch("/api/ai/improve-audit", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          prompt: aiPrompt,
          currentContent: auditContent,
        }),
      });

      if (!response.ok) {
        throw new Error("Błąd komunikacji z AI");
      }

      const data = await response.json();

      if (data.success) {
        setAuditContent(data.improvedContent);
        setShowAiModal(false);
        setAiPrompt("");
        toast.success("Treść została poprawiona przez AI");
      } else {
        throw new Error(data.error || "Błąd przetwarzania przez AI");
      }
    } catch (error) {
      console.error("Błąd przetwarzania przez AI:", error);
      toast.error("Wystąpił błąd podczas przetwarzania przez AI");
    } finally {
      setIsProcessingAi(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!domain) {
    return (
      <div className="text-center py-8">
        <p>Domena nie została znaleziona</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" asChild>
            <Link href={`/dashboard/domains/${domain.id}`}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Powrót do domeny
            </Link>
          </Button>
          <div className="flex gap-2">
            <h1 className="text-2xl font-bold flex items-center gap-2">
              <FileText className="h-6 w-6" />
              Edycja Audytu
            </h1>
            <div className="text-muted-foreground">
              {domain.domain} •{" "}
              <Badge variant="secondary">{domain.category}</Badge>
            </div>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button
            onClick={() => window.open(domain.fullDomain, "_blank")}
            variant="outline"
            size="sm"
          >
            <ExternalLink className="h-4 w-4 mr-2" />
            Otwórz domenę
          </Button>
        </div>
      </div>

      {/* Email Form - Above main content */}
      <Card>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-2">
            <div className="space-y-2">
              <Label htmlFor="recipientEmail">Adres odbiorcy</Label>
              <Input
                id="recipientEmail"
                type="email"
                value={emailData.recipientEmail}
                onChange={(e) =>
                  setEmailData({
                    ...emailData,
                    recipientEmail: e.target.value,
                  })
                }
                placeholder="<EMAIL>"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="subject">Temat wiadomości</Label>
              <Input
                id="subject"
                value={emailData.subject}
                onChange={(e) =>
                  setEmailData({ ...emailData, subject: e.target.value })
                }
                placeholder="Temat emaila"
              />
            </div>

            <div className="flex items-end gap-2">
              <Button
                onClick={sendTestEmail}
                disabled={isSendingEmail || !auditContent.trim() || !testEmailAddress.trim()}
                variant="outline"
                className="flex-1"
                title={!testEmailAddress.trim() ? "Skonfiguruj adres testowy w ustawieniach" : ""}
              >
                {isSendingEmail ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2"></div>
                    Wysyłanie...
                  </>
                ) : (
                  <>
                    <Send className="h-4 w-4 mr-2" />
                    {testEmailAddress.trim() ? "Wyślij testowego maila" : "Skonfiguruj email testowy"}
                  </>
                )}
              </Button>
              <Button
                onClick={() => setShowEmailConfirmDialog(true)}
                disabled={isSendingEmail || !auditContent.trim()}
                className="flex-1"
              >
                {isSendingEmail ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Wysyłanie...
                  </>
                ) : (
                  <>
                    <Send className="h-4 w-4 mr-2" />
                    Wyślij Email
                  </>
                )}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Audit Content Editor */}
        <Card className="lg:col-span-1">
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Treść Audytu
              </span>
              <div className="flex items-center gap-2">
                <Button
                  onClick={() => setShowAiModal(true)}
                  variant="outline"
                  size="sm"
                  disabled={!auditContent.trim()}
                >
                  <Sparkles className="h-4 w-4 mr-2" />
                  Popraw przez AI
                </Button>
                <Button
                  onClick={saveAuditContent}
                  disabled={isSaving}
                  size="sm"
                >
                  {isSaving ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Zapisywanie...
                    </>
                  ) : (
                    <>
                      <Save className="h-4 w-4 mr-2" />
                      Zapisz
                    </>
                  )}
                </Button>
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Textarea
              value={decodeAuditContent(auditContent)}
              onChange={(e) => setAuditContent(e.target.value)}
              placeholder="Wprowadź treść audytu bezpieczeństwa..."
              className="h-[70vh] font-mono text-sm"
              style={{ resize: "vertical" }}
            />
          </CardContent>
        </Card>

        {/* Email Preview */}
        <Card className="lg:col-span-1">
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span className="flex items-center gap-2">
                <Eye className="h-5 w-5" />
                Podgląd Emaila
              </span>
              <div className="flex items-center gap-2">
                <Button
                  variant={showFullHtmlPreview ? "default" : "outline"}
                  size="sm"
                  onClick={() => setShowFullHtmlPreview(!showFullHtmlPreview)}
                  disabled={!auditContent.trim()}
                >
                  <Code className="h-4 w-4 mr-2" />
                  {showFullHtmlPreview ? "Podgląd z stylami" : "Pełny HTML"}
                </Button>
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ScrollArea className="w-full">
              <div className="p-0">
                {auditContent ? (
                  <div className="space-y-4">
                    {showFullHtmlPreview ? (
                      /* Full HTML Preview with iframe */
                      <div className="border rounded-lg overflow-hidden">
                        <iframe
                          ref={iframeRef}
                          className="w-full h-[70vh]"
                          title="Email Preview"
                          style={{ border: 'none' }}
                        />
                      </div>
                    ) : (
                      /* Simple Content Preview */
                      <div className="border rounded-lg p-4 bg-white">
                        <div className="max-w-[960px]">
                          <div
                            className="text-sm"
                            dangerouslySetInnerHTML={{ __html: decodeAuditContent(auditContent) }}
                          />
                        </div>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    <Mail className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>Wprowadź treść audytu, aby zobaczyć podgląd emaila</p>
                  </div>
                )}
              </div>
            </ScrollArea>
          </CardContent>
        </Card>
      </div>

      {/* AI Improvement Modal */}
      <Dialog open={showAiModal} onOpenChange={setShowAiModal}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Sparkles className="h-5 w-5" />
              Popraw treść audytu przez AI
            </DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="aiPrompt">
                Opisz co chcesz zmienić lub poprawić w treści audytu:
              </Label>
              <Textarea
                id="aiPrompt"
                value={aiPrompt}
                onChange={(e) => setAiPrompt(e.target.value)}
                placeholder="np. Dodaj więcej szczegółów technicznych, popraw formatowanie, uprość język..."
                className="min-h-[120px]"
              />
            </div>

            <div className="bg-muted p-3 rounded-lg">
              <p className="text-sm text-muted-foreground">
                <strong>Obecna treść audytu:</strong> {decodeAuditContent(auditContent).length}{" "}
                znaków
              </p>
              <p className="text-xs text-muted-foreground mt-1">
                AI przeanalizuje obecną treść i wprowadzi zmiany zgodnie z Twoim
                promptem.
              </p>
            </div>

            <div className="flex justify-end gap-2">
              <Button
                variant="outline"
                onClick={() => {
                  setShowAiModal(false);
                  setAiPrompt("");
                }}
                disabled={isProcessingAi}
              >
                Anuluj
              </Button>
              <Button
                onClick={processWithAi}
                disabled={isProcessingAi || !aiPrompt.trim()}
              >
                {isProcessingAi ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Przetwarzanie...
                  </>
                ) : (
                  <>
                    <Sparkles className="h-4 w-4 mr-2" />
                    Popraw przez AI
                  </>
                )}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Email Confirmation Dialog */}
      <AlertDialog open={showEmailConfirmDialog} onOpenChange={setShowEmailConfirmDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Potwierdzenie wysłania emaila</AlertDialogTitle>
            <AlertDialogDescription>
              {(() => {
                const firstEmail = getFirstEmail(emailData.recipientEmail);
                const hasMultipleEmails = emailData.recipientEmail.includes(',');

                return (
                  <>
                    Czy na pewno chcesz wysłać email z audytem do <strong>{firstEmail}</strong>?
                    {hasMultipleEmails && (
                      <>
                        <br />
                        <span className="text-muted-foreground text-sm">
                          (Zostanie użyty pierwszy z podanych adresów)
                        </span>
                      </>
                    )}
                    <br />
                    <br />
                    <span className="text-destructive font-medium">
                      Tej czynności nie da się cofnąć.
                    </span>
                  </>
                );
              })()}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Anuluj</AlertDialogCancel>
            <AlertDialogAction
              onClick={sendAuditEmail}
              disabled={isSendingEmail}
            >
              {isSendingEmail ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Wysyłanie...
                </>
              ) : (
                "Wyślij Email"
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
