"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { toast } from "sonner";
import {
  Plus,
  Edit,
  Trash2,
  Save,
  X,
  Bug,
  Search,
  ChevronLeft,
  ChevronRight,
} from "lucide-react";
import { Textarea } from "@/components/ui/textarea";

interface Vulnerability {
  id: string;
  vulnerability_id: string;
  vulnerability_name: string;
  volnerability_description: string;
  createdAt: string;
  updatedAt: string;
}

interface VulnerabilitiesResponse {
  success: boolean;
  vulnerabilities: Vulnerability[];
  pagination: {
    page: number;
    limit: number;
    totalCount: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export default function PodatnosciPage() {
  const [vulnerabilities, setVulnerabilities] = useState<Vulnerability[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isAdding, setIsAdding] = useState(false);
  const [editingId, setEditingId] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const [totalPages, setTotalPages] = useState(0);
  const [hasNext, setHasNext] = useState(false);
  const [hasPrev, setHasPrev] = useState(false);
  
  const [newVulnerability, setNewVulnerability] = useState({
    vulnerability_id: "",
    vulnerability_name: "",
    volnerability_description: "",
  });
  const [editVulnerability, setEditVulnerability] = useState({
    vulnerability_id: "",
    vulnerability_name: "",
    volnerability_description: "",
  });

  const fetchVulnerabilities = async (page = 1, search = "") => {
    setIsLoading(true);
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: "10",
      });
      
      if (search) {
        params.append("search", search);
      }
      
      const response = await fetch(`/api/vulnerabilities?${params}`);
      if (!response.ok) {
        throw new Error("Błąd pobierania podatności");
      }
      const data: VulnerabilitiesResponse = await response.json();
      
      setVulnerabilities(data.vulnerabilities);
      setTotalCount(data.pagination.totalCount);
      setTotalPages(data.pagination.totalPages);
      setHasNext(data.pagination.hasNext);
      setHasPrev(data.pagination.hasPrev);
      setCurrentPage(data.pagination.page);
    } catch (error) {
      console.error("Błąd pobierania podatności:", error);
      toast.error("Wystąpił błąd podczas pobierania podatności");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchVulnerabilities(currentPage, searchTerm);
  }, [currentPage]);

  const handleSearch = () => {
    setCurrentPage(1);
    fetchVulnerabilities(1, searchTerm);
  };

  const handlePageChange = (newPage: number) => {
    setCurrentPage(newPage);
    fetchVulnerabilities(newPage, searchTerm);
  };

  const handleAddVulnerability = async () => {
    if (!newVulnerability.vulnerability_id.trim()) {
      toast.error("ID podatności jest wymagane");
      return;
    }

    if (!newVulnerability.vulnerability_name.trim()) {
      toast.error("Nazwa podatności jest wymagana");
      return;
    }

    if (!newVulnerability.volnerability_description.trim()) {
      toast.error("Opis podatności jest wymagany");
      return;
    }

    try {
      const response = await fetch("/api/vulnerabilities", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(newVulnerability),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Błąd tworzenia podatności");
      }

      setNewVulnerability({
        vulnerability_id: "",
        vulnerability_name: "",
        volnerability_description: "",
      });
      setIsAdding(false);
      toast.success("Podatność została dodana");
      
      // Refresh the list
      fetchVulnerabilities(currentPage, searchTerm);
    } catch (error) {
      console.error("Błąd dodawania podatności:", error);
      toast.error((error instanceof Error ? error.message : String(error)) || "Wystąpił błąd podczas dodawania podatności");
    }
  };

  const handleEditVulnerability = async (id: string) => {
    if (!editVulnerability.vulnerability_id.trim()) {
      toast.error("ID podatności jest wymagane");
      return;
    }

    if (!editVulnerability.vulnerability_name.trim()) {
      toast.error("Nazwa podatności jest wymagana");
      return;
    }

    if (!editVulnerability.volnerability_description.trim()) {
      toast.error("Opis podatności jest wymagany");
      return;
    }

    try {
      const response = await fetch(`/api/vulnerabilities/${id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(editVulnerability),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Błąd aktualizacji podatności");
      }

      setEditingId(null);
      setEditVulnerability({
        vulnerability_id: "",
        vulnerability_name: "",
        volnerability_description: "",
      });
      toast.success("Podatność została zaktualizowana");
      
      // Refresh the list
      fetchVulnerabilities(currentPage, searchTerm);
    } catch (error) {
      console.error("Błąd aktualizacji podatności:", error);
      toast.error((error instanceof Error ? error.message : String(error)) || "Wystąpił błąd podczas aktualizacji podatności");
    }
  };

  const handleDeleteVulnerability = async (id: string) => {
    if (!confirm("Czy na pewno chcesz usunąć tę podatność?")) {
      return;
    }

    try {
      const response = await fetch(`/api/vulnerabilities/${id}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Błąd usuwania podatności");
      }

      toast.success("Podatność została usunięta");
      
      // If we're on the last page and it would be empty, go to previous page
      if (vulnerabilities.length === 1 && currentPage > 1) {
        setCurrentPage(currentPage - 1);
        fetchVulnerabilities(currentPage - 1, searchTerm);
      } else {
        fetchVulnerabilities(currentPage, searchTerm);
      }
    } catch (error) {
      console.error("Błąd usuwania podatności:", error);
      toast.error((error instanceof Error ? error.message : String(error)) || "Wystąpił błąd podczas usuwania podatności");
    }
  };

  const startEditVulnerability = (vulnerability: Vulnerability) => {
    setEditingId(vulnerability.id);
    setEditVulnerability({
      vulnerability_id: vulnerability.vulnerability_id,
      vulnerability_name: vulnerability.vulnerability_name,
      volnerability_description: vulnerability.volnerability_description,
    });
  };

  const cancelEditVulnerability = () => {
    setEditingId(null);
    setEditVulnerability({
      vulnerability_id: "",
      vulnerability_name: "",
      volnerability_description: "",
    });
  };

  const cancelAddVulnerability = () => {
    setIsAdding(false);
    setNewVulnerability({
      vulnerability_id: "",
      vulnerability_name: "",
      volnerability_description: "",
    });
  };

  return (
    <div className="space-y-6">
      {/* Nagłówek */}
      <div>
        <h1 className="text-3xl font-bold text-foreground flex items-center gap-2">
          <Bug className="h-8 w-8" />
          Podatności ({totalCount})
        </h1>
        <p className="text-muted-foreground mt-2">
          Zarządzaj bazą podatności bezpieczeństwa
        </p>
      </div>

      {/* Wyszukiwarka */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex gap-4">
            <div className="flex-1">
              <Input
                placeholder="Szukaj podatności..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                onKeyPress={(e) => e.key === "Enter" && handleSearch()}
              />
            </div>
            <Button onClick={handleSearch}>
              <Search className="h-4 w-4 mr-2" />
              Szukaj
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Podatności */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle className="flex items-center gap-2">
              <Bug className="h-5 w-5" />
              Lista podatności
            </CardTitle>
            <Button
              onClick={() => setIsAdding(true)}
              disabled={isAdding}
              className="flex items-center gap-2"
            >
              <Plus className="h-4 w-4" />
              Dodaj podatność
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Formularz dodawania nowej podatności */}
            {isAdding && (
              <div className="border rounded-lg p-4 bg-muted/50">
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="new-vulnerability-id">
                        ID podatności
                      </Label>
                      <Input
                        id="new-vulnerability-id"
                        placeholder="np. CVE-2023-1234"
                        value={newVulnerability.vulnerability_id}
                        onChange={(e) =>
                          setNewVulnerability({
                            ...newVulnerability,
                            vulnerability_id: e.target.value,
                          })
                        }
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="new-vulnerability-name">
                        Nazwa podatności
                      </Label>
                      <Input
                        id="new-vulnerability-name"
                        placeholder="np. SQL Injection"
                        value={newVulnerability.vulnerability_name}
                        onChange={(e) =>
                          setNewVulnerability({
                            ...newVulnerability,
                            vulnerability_name: e.target.value,
                          })
                        }
                      />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="new-vulnerability-description">
                      Opis podatności
                    </Label>
                    <Textarea
                      id="new-vulnerability-description"
                      placeholder="Opis podatności..."
                      rows={4}
                      value={newVulnerability.volnerability_description}
                      onChange={(e) =>
                        setNewVulnerability({
                          ...newVulnerability,
                          volnerability_description: e.target.value,
                        })
                      }
                    />
                  </div>
                  <div className="flex gap-2">
                    <Button onClick={handleAddVulnerability} size="sm">
                      <Save className="h-4 w-4 mr-2" />
                      Zapisz
                    </Button>
                    <Button
                      onClick={cancelAddVulnerability}
                      variant="outline"
                      size="sm"
                    >
                      <X className="h-4 w-4 mr-2" />
                      Anuluj
                    </Button>
                  </div>
                </div>
              </div>
            )}

            {/* Lista podatności */}
            {isLoading ? (
              <div className="text-center py-8">
                <p className="text-muted-foreground">Ładowanie podatności...</p>
              </div>
            ) : vulnerabilities.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-muted-foreground">
                  {searchTerm ? "Nie znaleziono podatności." : "Brak podatności. Dodaj pierwszą podatność."}
                </p>
              </div>
            ) : (
              <div className="space-y-2">
                {vulnerabilities.map((vulnerability) => (
                  <div
                    key={vulnerability.id}
                    className="border rounded-lg p-4 flex items-start justify-between"
                  >
                    {editingId === vulnerability.id ? (
                      <div className="flex-1 mr-4">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                          <div className="space-y-2">
                            <Label>ID podatności</Label>
                            <Input
                              value={editVulnerability.vulnerability_id}
                              onChange={(e) =>
                                setEditVulnerability({
                                  ...editVulnerability,
                                  vulnerability_id: e.target.value,
                                })
                              }
                            />
                          </div>
                          <div className="space-y-2">
                            <Label>Nazwa podatności</Label>
                            <Input
                              value={editVulnerability.vulnerability_name}
                              onChange={(e) =>
                                setEditVulnerability({
                                  ...editVulnerability,
                                  vulnerability_name: e.target.value,
                                })
                              }
                            />
                          </div>
                        </div>
                        <div className="space-y-2">
                          <Label>Opis podatności</Label>
                          <Textarea
                            rows={4}
                            value={editVulnerability.volnerability_description}
                            onChange={(e) =>
                              setEditVulnerability({
                                ...editVulnerability,
                                volnerability_description: e.target.value,
                              })
                            }
                          />
                        </div>
                      </div>
                    ) : (
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <Bug className="h-4 w-4 text-muted-foreground" />
                          <h3 className="font-medium">
                            {vulnerability.vulnerability_name}
                          </h3>
                          <span className="text-xs bg-muted px-2 py-1 rounded">
                            {vulnerability.vulnerability_id}
                          </span>
                        </div>
                        <p className="text-sm text-muted-foreground">
                          {vulnerability.volnerability_description}
                        </p>
                      </div>
                    )}

                    <div className="flex gap-2">
                      {editingId === vulnerability.id ? (
                        <>
                          <Button
                            onClick={() => handleEditVulnerability(vulnerability.id)}
                            size="sm"
                            variant="default"
                          >
                            <Save className="h-4 w-4" />
                          </Button>
                          <Button
                            onClick={cancelEditVulnerability}
                            size="sm"
                            variant="outline"
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </>
                      ) : (
                        <>
                          <Button
                            onClick={() => startEditVulnerability(vulnerability)}
                            size="sm"
                            variant="outline"
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            onClick={() => handleDeleteVulnerability(vulnerability.id)}
                            size="sm"
                            variant="destructive"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}

            {/* Paginacja */}
            {totalPages > 1 && (
              <div className="flex items-center justify-between pt-4 border-t">
                <div className="text-sm text-muted-foreground">
                  Strona {currentPage} z {totalPages} ({totalCount} podatności)
                </div>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handlePageChange(currentPage - 1)}
                    disabled={!hasPrev}
                  >
                    <ChevronLeft className="h-4 w-4" />
                    Poprzednia
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handlePageChange(currentPage + 1)}
                    disabled={!hasNext}
                  >
                    Następna
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}