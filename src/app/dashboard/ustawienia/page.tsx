"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON>Content, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { toast } from "sonner";
import {
  Settings,
  Plus,
  Edit,
  Trash2,
  Save,
  X,
  Database,
  Zap,
  Mail,
  Server,
  Globe,
  Shield,
  Tag,
  Star,
  CheckCircle,
  AlertCircle,
  Code,
  Lock,
  Heart,
  Home,
  User,
  Users,
  Calendar,
  FileText,
  Image,
  Search,
  Bell,
  MessageCircle,
  Phone,
  MapPin,
  Link,
  TrendingUp,
  BarChart,
  Activity,
  Target,
  Award,
  ShoppingCart,
  CreditCard,
  DollarSign,
  Briefcase,
  Building,
  Truck,
  Coffee,
  Lightbulb,
  Sun,
  Moon,
  Cloud,
  Key,
} from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";

interface Category {
  id: string;
  name: string;
  description?: string;
  createdAt: string;
  updatedAt: string;
}

interface CategoriesResponse {
  success: boolean;
  categories: Category[];
}

interface Domain {
  id: string;
  domain: string;
  fullDomain: string;
  category: string;
  linkCount: number;
  status: string;
  createdAt: string;
}

interface DomainsResponse {
  success: boolean;
  domains: Domain[];
}

interface MetadataButton {
  id: string;
  label: string;
  key: string;
  value: string | number | boolean;
  icon: string;
  description?: string;
  order: number;
}

interface MetadataButtonsResponse {
  success: boolean;
  metadataButtons: MetadataButton[];
}



export default function UstawieniaPage() {
  const [categories, setCategories] = useState<Category[]>([]);
  const [categoryDomains, setCategoryDomains] = useState<Record<string, Domain[]>>({});
  const [isLoading, setIsLoading] = useState(true);
  const [isAdding, setIsAdding] = useState(false);
  const [editingId, setEditingId] = useState<string | null>(null);
  const [newCategory, setNewCategory] = useState({ name: "", description: "" });
  const [editCategory, setEditCategory] = useState({
    name: "",
    description: "",
  });

  // Stany dla przycisków metadata
  const [metadataButtons, setMetadataButtons] = useState<MetadataButton[]>([]);
  const [isLoadingButtons, setIsLoadingButtons] = useState(true);
  const [isAddingButton, setIsAddingButton] = useState(false);
  const [editingButtonId, setEditingButtonId] = useState<string | null>(null);
  const [newButton, setNewButton] = useState({
    label: "",
    key: "",
    value: "",
    icon: "",
    description: "",
  });
  const [editButton, setEditButton] = useState({
    label: "",
    key: "",
    value: "",
    icon: "",
    description: "",
  });

  // Stany dla testowego emaila
  const [testEmail, setTestEmail] = useState("");
  const [isLoadingTestEmail, setIsLoadingTestEmail] = useState(true);
  const [isSavingTestEmail, setIsSavingTestEmail] = useState(false);


  const fetchCategories = async () => {
    setIsLoading(true);
    try {
      const response = await fetch("/api/categories");

      if (!response.ok) {
        throw new Error("Błąd pobierania kategorii");
      }

      const data: CategoriesResponse = await response.json();
      setCategories(data.categories);
      
      // Pobierz domeny dla każdej kategorii
      await fetchDomainsForCategories(data.categories);
    } catch (error) {
      console.error("Błąd pobierania kategorii:", error);
      toast.error("Wystąpił błąd podczas pobierania kategorii");
    } finally {
      setIsLoading(false);
    }
  };

  const fetchDomainsForCategories = async (categories: Category[]) => {
    const domainsPromises = categories.map(async (category) => {
      try {
        const response = await fetch(`/api/domains?category=${encodeURIComponent(category.name)}&limit=10000`);
        if (response.ok) {
          const data: DomainsResponse = await response.json();
          return { categoryName: category.name, domains: data.domains };
        }
        return { categoryName: category.name, domains: [] };
      } catch (error) {
        console.error(`Błąd pobierania domen dla kategorii ${category.name}:`, error);
        return { categoryName: category.name, domains: [] };
      }
    });

    try {
      const results = await Promise.all(domainsPromises);
      const newDomainsMap = results.reduce((acc, result) => {
        acc[result.categoryName] = result.domains;
        return acc;
      }, {} as Record<string, Domain[]>);
      
      setCategoryDomains((prev) => ({
        ...prev,
        ...newDomainsMap
      }));
    } catch (error) {
      console.error("Błąd pobierania domen:", error);
    }
  };

  const fetchMetadataButtons = async () => {
    try {
      const response = await fetch("/api/settings/metadata-buttons");
      if (!response.ok) {
        throw new Error("Błąd pobierania przycisków metadata");
      }
      const data: MetadataButtonsResponse = await response.json();
      setMetadataButtons(data.metadataButtons);
    } catch (error) {
      console.error("Błąd pobierania przycisków metadata:", error);
      toast.error(
        (error instanceof Error ? error.message : String(error)) || "Wystąpił błąd podczas pobierania przycisków metadata"
      );
    } finally {
      setIsLoadingButtons(false);
    }
  };

  // Funkcje dla testowego emaila
  const fetchTestEmail = async () => {
    setIsLoadingTestEmail(true);
    try {
      const response = await fetch("/api/settings/test-email");

      if (!response.ok) {
        throw new Error("Błąd pobierania testowego emaila");
      }

      const data = await response.json();
      setTestEmail(data.testEmail || "");
    } catch (error) {
      console.error("Błąd pobierania testowego emaila:", error);
      toast.error("Wystąpił błąd podczas pobierania testowego emaila");
    } finally {
      setIsLoadingTestEmail(false);
    }
  };

  const handleSaveTestEmail = async () => {
    setIsSavingTestEmail(true);
    try {
      const response = await fetch("/api/settings/test-email", {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ testEmail }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Błąd zapisywania testowego emaila");
      }

      toast.success("Testowy email został zapisany");
    } catch (error) {
      console.error("Błąd zapisywania testowego emaila:", error);
      toast.error((error instanceof Error ? error.message : String(error)) || "Wystąpił błąd podczas zapisywania testowego emaila");
    } finally {
      setIsSavingTestEmail(false);
    }
  };


  useEffect(() => {
    fetchCategories();
    fetchMetadataButtons();
    fetchTestEmail();
  }, []);

  const handleAddCategory = async () => {
    if (!newCategory.name.trim()) {
      toast.error("Nazwa kategorii jest wymagana");
      return;
    }

    try {
      const response = await fetch("/api/categories", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(newCategory),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Błąd tworzenia kategorii");
      }

      const data = await response.json();
      const updatedCategories = [...categories, data.category];
      setCategories(updatedCategories);
      setNewCategory({ name: "", description: "" });
      setIsAdding(false);
      toast.success("Kategoria została dodana");
      
      // Pobierz domeny dla nowej kategorii
      await fetchDomainsForCategories([data.category]);
    } catch (error) {
      console.error("Błąd dodawania kategorii:", error);
      toast.error((error instanceof Error ? error.message : String(error)) || "Wystąpił błąd podczas dodawania kategorii");
    }
  };

  const handleEditCategory = async (id: string) => {
    if (!editCategory.name.trim()) {
      toast.error("Nazwa kategorii jest wymagana");
      return;
    }

    try {
      const response = await fetch(`/api/categories/${id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(editCategory),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Błąd aktualizacji kategorii");
      }

      const data = await response.json();
      const updatedCategories = categories.map((cat) => (cat.id === id ? data.category : cat));
      setCategories(updatedCategories);
      setEditingId(null);
      setEditCategory({ name: "", description: "" });
      toast.success("Kategoria została zaktualizowana");
      
      // Pobierz domeny dla zaktualizowanej kategorii
      await fetchDomainsForCategories([data.category]);
    } catch (error) {
      console.log(error)
      console.error("Błąd aktualizacji kategorii");
      toast.error(
         "Wystąpił błąd podczas aktualizacji kategorii"
      );
    }
  };

  const handleDeleteCategory = async (id: string) => {
    if (!confirm("Czy na pewno chcesz usunąć tę kategorię?")) {
      return;
    }

    try {
      const response = await fetch(`/api/categories/${id}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Błąd usuwania kategorii");
      }

      const deletedCategory = categories.find((cat) => cat.id === id);
      setCategories(categories.filter((cat) => cat.id !== id));
      
      // Usuń również domeny tej kategorii z pamięci
      if (deletedCategory) {
        setCategoryDomains((prev) => {
          const updated = { ...prev };
          delete updated[deletedCategory.name];
          return updated;
        });
      }
      
      toast.success("Kategoria została usunięta");
    } catch (error) {
      console.error("Błąd usuwania kategorii:", error);
      toast.error((error instanceof Error ? error.message : String(error)) || "Wystąpił błąd podczas usuwania kategorii");
    }
  };

  const handleDeleteAllDomains = async (categoryId: string, categoryName: string) => {
    const domainCount = categoryDomains[categoryName]?.length || 0;
    if (!confirm(`Czy na pewno chcesz usunąć wszystkie ${domainCount} domen z kategorii "${categoryName}"?`)) {
      return;
    }

    try {
      const response = await fetch(`/api/categories/${categoryId}/domains`, {
        method: "DELETE",
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Błąd usuwania domen");
      }

      const data = await response.json();
      
      // Aktualizuj domeny w pamięci
      setCategoryDomains((prev) => ({
        ...prev,
        [categoryName]: []
      }));
      
      toast.success(data.message);
    } catch (error) {
      console.error("Błąd usuwania domen:", error);
      toast.error((error instanceof Error ? error.message : String(error)) || "Wystąpił błąd podczas usuwania domen");
    }
  };

  const handleDeleteAllSearches = async (categoryId: string, categoryName: string) => {
    if (!confirm(`Czy na pewno chcesz usunąć wszystkie wyszukiwania z kategorii "${categoryName}"?`)) {
      return;
    }

    try {
      const response = await fetch(`/api/categories/${categoryId}/searches`, {
        method: "DELETE",
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Błąd usuwania wyszukiwań");
      }

      const data = await response.json();
      toast.success(data.message);
    } catch (error) {
      console.error("Błąd usuwania wyszukiwań:", error);
      toast.error((error instanceof Error ? error.message : String(error)) || "Wystąpił błąd podczas usuwania wyszukiwań");
    }
  };

  const startEdit = (category: Category) => {
    setEditingId(category.id);
    setEditCategory({
      name: category.name,
      description: category.description || "",
    });
  };

  const cancelEdit = () => {
    setEditingId(null);
    setEditCategory({ name: "", description: "" });
  };

  const cancelAdd = () => {
    setIsAdding(false);
    setNewCategory({ name: "", description: "" });
  };

  // Funkcje dla przycisków metadata
  const handleAddButton = async () => {
    if (!newButton.label.trim()) {
      toast.error("Etykieta przycisku jest wymagana");
      return;
    }

    if (!newButton.key.trim()) {
      toast.error("Klucz metadata jest wymagany");
      return;
    }

    if (!newButton.value.trim()) {
      toast.error("Wartość jest wymagana");
      return;
    }

    if (!newButton.icon.trim()) {
      toast.error("Ikona jest wymagana");
      return;
    }

    try {
      const response = await fetch("/api/settings/metadata-buttons", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          label: newButton.label.trim(),
          key: newButton.key.trim(),
          value: newButton.value.trim(),
          icon: newButton.icon.trim(),
          description: newButton.description.trim(),
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Błąd tworzenia przycisku");
      }

      const data = await response.json();
      setMetadataButtons([...metadataButtons, data.button]);
      setNewButton({
        label: "",
        key: "",
        value: "",
        icon: "",
        description: "",
      });
      setIsAddingButton(false);
      toast.success("Przycisk metadata został dodany");
    } catch (error) {
      console.error("Błąd dodawania przycisku", error);
      toast.error("Wystąpił błąd podczas dodawania przycisku");
    }
  };

  const handleEditButton = async (id: string) => {
    if (!editButton.label.trim()) {
      toast.error("Etykieta przycisku jest wymagana");
      return;
    }

    if (!editButton.key.trim()) {
      toast.error("Klucz metadata jest wymagany");
      return;
    }

    if (!editButton.value.trim()) {
      toast.error("Wartość jest wymagana");
      return;
    }

    if (!editButton.icon.trim()) {
      toast.error("Ikona jest wymagana");
      return;
    }

    try {
      const response = await fetch(`/api/settings/metadata-buttons/${id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          label: editButton.label.trim(),
          key: editButton.key.trim(),
          value: editButton.value.trim(),
          icon: editButton.icon.trim(),
          description: editButton.description.trim(),
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Błąd aktualizacji przycisku");
      }

      const data = await response.json();
      setMetadataButtons(
        metadataButtons.map((btn) => (btn.id === id ? data.button : btn))
      );
      setEditingButtonId(null);
      setEditButton({
        label: "",
        key: "",
        value: "",
        icon: "",
        description: "",
      });
      toast.success("Przycisk metadata został zaktualizowany");
    } catch (error) {
      console.error("Błąd edycji przycisku", error);
      toast.error(error instanceof Error ? error.message : "Wystąpił błąd podczas edycji przycisku");
    }
  };

  const handleDeleteButton = async (id: string) => {
    if (!confirm("Czy na pewno chcesz usunąć ten przycisk?")) {
      return;
    }

    try {
      const response = await fetch(`/api/settings/metadata-buttons/${id}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Błąd usuwania przycisku");
      }

      setMetadataButtons(metadataButtons.filter((btn) => btn.id !== id));
      toast.success("Przycisk metadata został usunięty");
    } catch (error) {
      console.error("Błąd usuwania przycisku", error);
      toast.error("Wystąpił błąd podczas usuwania przycisku");
    }
  };

  const startEditButton = (button: MetadataButton) => {
    setEditingButtonId(button.id);
    setEditButton({
      label: button.label,
      key: button.key,
      value: button.value.toString(),
      icon: button.icon,
      description: button.description || "",
    });
  };

  const cancelEditButton = () => {
    setEditingButtonId(null);
    setEditButton({ label: "", key: "", value: "", icon: "", description: "" });
  };

  const cancelAddButton = () => {
    setIsAddingButton(false);
    setNewButton({ label: "", key: "", value: "", icon: "", description: "" });
  };

  // Funkcja do renderowania ikon
  const renderIcon = (iconName: string, className: string = "h-4 w-4") => {
    const iconProps = { className };

    switch (iconName) {
      case "Server":
        return <Server {...iconProps} />;
      case "Database":
        return <Database {...iconProps} />;
      case "Globe":
        return <Globe {...iconProps} />;
      case "Shield":
        return <Shield {...iconProps} />;
      case "Zap":
        return <Zap {...iconProps} />;
      case "Settings":
        return <Settings {...iconProps} />;
      case "Tag":
        return <Tag {...iconProps} />;
      case "Star":
        return <Star {...iconProps} />;
      case "CheckCircle":
        return <CheckCircle {...iconProps} />;
      case "AlertCircle":
        return <AlertCircle {...iconProps} />;
      case "Code":
        return <Code {...iconProps} />;
      case "Lock":
        return <Lock {...iconProps} />;
      case "Heart":
        return <Heart {...iconProps} />;
      case "Home":
        return <Home {...iconProps} />;
      case "User":
        return <User {...iconProps} />;
      case "Users":
        return <Users {...iconProps} />;
      case "Calendar":
        return <Calendar {...iconProps} />;
      case "FileText":
        return <FileText {...iconProps} />;
      case "Image":
        return <Image {...iconProps} />;
      case "Search":
        return <Search {...iconProps} />;
      case "Bell":
        return <Bell {...iconProps} />;
      case "MessageCircle":
        return <MessageCircle {...iconProps} />;
      case "Phone":
        return <Phone {...iconProps} />;
      case "MapPin":
        return <MapPin {...iconProps} />;
      case "Link":
        return <Link {...iconProps} />;
      case "TrendingUp":
        return <TrendingUp {...iconProps} />;
      case "BarChart":
        return <BarChart {...iconProps} />;
      case "Activity":
        return <Activity {...iconProps} />;
      case "Target":
        return <Target {...iconProps} />;
      case "Award":
        return <Award {...iconProps} />;
      case "ShoppingCart":
        return <ShoppingCart {...iconProps} />;
      case "CreditCard":
        return <CreditCard {...iconProps} />;
      case "DollarSign":
        return <DollarSign {...iconProps} />;
      case "Briefcase":
        return <Briefcase {...iconProps} />;
      case "Building":
        return <Building {...iconProps} />;
      case "Truck":
        return <Truck {...iconProps} />;
      case "Coffee":
        return <Coffee {...iconProps} />;
      case "Lightbulb":
        return <Lightbulb {...iconProps} />;
      case "Sun":
        return <Sun {...iconProps} />;
      case "Moon":
        return <Moon {...iconProps} />;
      case "Cloud":
        return <Cloud {...iconProps} />;
      case "Key":
        return <Key {...iconProps} />;
      case "Mail":
        return <Mail {...iconProps} />;
      default:
        return <Database {...iconProps} />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Nagłówek */}
      <div>
        <h1 className="text-3xl font-bold text-foreground flex items-center gap-2">
          <Settings className="h-8 w-8" />
          Ustawienia
        </h1>
        <p className="text-muted-foreground mt-2">
          Zarządzaj kategoriami i przyciskami metadata
        </p>
      </div>

      {/* Kategorie */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle>Kategorie</CardTitle>
            <Button
              onClick={() => setIsAdding(true)}
              disabled={isAdding}
              className="flex items-center gap-2"
            >
              <Plus className="h-4 w-4" />
              Dodaj kategorię
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Formularz dodawania nowej kategorii */}
            {isAdding && (
              <div className="border rounded-lg p-4 bg-muted/50">
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="new-name">Nazwa kategorii</Label>
                      <Input
                        id="new-name"
                        placeholder="np. Programowanie"
                        value={newCategory.name}
                        onChange={(e) =>
                          setNewCategory({
                            ...newCategory,
                            name: e.target.value,
                          })
                        }
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="new-description">Opis (opcjonalny)</Label>
                      <Input
                        id="new-description"
                        placeholder="Opis kategorii"
                        value={newCategory.description}
                        onChange={(e) =>
                          setNewCategory({
                            ...newCategory,
                            description: e.target.value,
                          })
                        }
                      />
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <Button onClick={handleAddCategory} size="sm">
                      <Save className="h-4 w-4 mr-2" />
                      Zapisz
                    </Button>
                    <Button onClick={cancelAdd} variant="outline" size="sm">
                      <X className="h-4 w-4 mr-2" />
                      Anuluj
                    </Button>
                  </div>
                </div>
              </div>
            )}

            {/* Lista kategorii */}
            {isLoading ? (
              <div className="text-center py-8">
                <p className="text-muted-foreground">Ładowanie kategorii...</p>
              </div>
            ) : categories.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-muted-foreground">
                  Brak kategorii. Dodaj pierwszą kategorię.
                </p>
              </div>
            ) : (
              <div className="space-y-2">
                {categories.map((category) => (
                  <div
                    key={category.id}
                    className="border rounded-lg p-4 flex items-center justify-between"
                  >
                    {editingId === category.id ? (
                      <div className="flex-1 grid grid-cols-1 md:grid-cols-2 gap-4 mr-4">
                        <div className="space-y-2">
                          <Label>Nazwa kategorii</Label>
                          <Input
                            value={editCategory.name}
                            onChange={(e) =>
                              setEditCategory({
                                ...editCategory,
                                name: e.target.value,
                              })
                            }
                          />
                        </div>
                        <div className="space-y-2">
                          <Label>Opis</Label>
                          <Input
                            value={editCategory.description}
                            onChange={(e) =>
                              setEditCategory({
                                ...editCategory,
                                description: e.target.value,
                              })
                            }
                          />
                        </div>
                      </div>
                    ) : (
                      <div className="flex-1">
                        <div className="flex items-center gap-2">
                          <h3 className="font-medium">{category.name}</h3>
                          <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                            {categoryDomains[category.name]?.length || 0} domen
                          </span>
                        </div>
                        {category.description && (
                          <p className="text-sm text-muted-foreground mt-1">
                            {category.description}
                          </p>
                        )}
                        
                        {/* Lista domen w kategorii */}
                        {categoryDomains[category.name] && categoryDomains[category.name].length > 0 && (
                          <div className="mt-3 p-3 bg-gray-50 rounded-lg">
                            <h4 className="text-sm font-medium text-gray-700 mb-2">Domeny w tej kategorii:</h4>
                            <div className="space-y-1">
                              {categoryDomains[category.name].slice(0, 5).map((domain) => (
                                <div key={domain.id} className="flex items-center justify-between text-xs">
                                  <span className="text-gray-600 font-mono">{domain.fullDomain}</span>
                                  <div className="flex items-center gap-1">
                                    <span className={`px-2 py-1 rounded text-xs ${
                                      domain.status === 'accepted' ? 'bg-green-100 text-green-800' :
                                      domain.status === 'rejected' ? 'bg-red-100 text-red-800' :
                                      domain.status === 'new' ? 'bg-yellow-100 text-yellow-800' :
                                      'bg-gray-100 text-gray-800'
                                    }`}>
                                      {domain.status}
                                    </span>
                                    <span className="text-gray-500">({domain.linkCount} linków)</span>
                                  </div>
                                </div>
                              ))}
                              {categoryDomains[category.name].length > 5 && (
                                <div className="text-xs text-gray-500 mt-1">
                                  ... i {categoryDomains[category.name].length - 5} więcej
                                </div>
                              )}
                            </div>
                          </div>
                        )}
                      </div>
                    )}

                    <div className="flex gap-2">
                      {editingId === category.id ? (
                        <>
                          <Button
                            onClick={() => handleEditCategory(category.id)}
                            size="sm"
                            variant="default"
                          >
                            <Save className="h-4 w-4" />
                          </Button>
                          <Button
                            onClick={cancelEdit}
                            size="sm"
                            variant="outline"
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </>
                      ) : (
                        <>
                          {categoryDomains[category.name]?.length > 0 && (
                            <>
                              <Button
                                onClick={() => handleDeleteAllSearches(category.id, category.name)}
                                size="sm"
                                variant="outline"
                                title="Usuń wszystkie wyszukiwania z tej kategorii"
                              >
                                <Search className="h-4 w-4" />
                                <Trash2 className="h-3 w-3 ml-1" />
                              </Button>
                              <Button
                                onClick={() => handleDeleteAllDomains(category.id, category.name)}
                                size="sm"
                                variant="outline"
                                title="Usuń wszystkie domeny z tej kategorii"
                              >
                                <Globe className="h-4 w-4" />
                                <Trash2 className="h-3 w-3 ml-1" />
                              </Button>
                            </>
                          )}
                          <Button
                            onClick={() => startEdit(category)}
                            size="sm"
                            variant="outline"
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            onClick={() => handleDeleteCategory(category.id)}
                            size="sm"
                            variant="destructive"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Ustawienia Email */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Mail className="h-5 w-5" />
            Ustawienia Email
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="test-email">
                Adres email do testów
              </Label>
              <div className="flex gap-2">
                <Input
                  id="test-email"
                  type="email"
                  placeholder="np. <EMAIL>"
                  value={testEmail}
                  onChange={(e) => setTestEmail(e.target.value)}
                  disabled={isLoadingTestEmail || isSavingTestEmail}
                  className="flex-1"
                />
                <Button
                  onClick={handleSaveTestEmail}
                  disabled={isSavingTestEmail || isLoadingTestEmail}
                  className="min-w-[100px]"
                >
                  {isSavingTestEmail ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Zapisywanie...
                    </>
                  ) : (
                    <>
                      <Save className="h-4 w-4 mr-2" />
                      Zapisz
                    </>
                  )}
                </Button>
              </div>
              <p className="text-sm text-muted-foreground">
                Ten adres email będzie używany do wysyłania testowych maili z audytami.
                Pozostaw puste, aby wyłączyć funkcję testowych maili.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Przyciski Metadata */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle className="flex items-center gap-2">
              <Zap className="h-5 w-5" />
              Szybkie przyciski metadata
            </CardTitle>
            <Button
              onClick={() => setIsAddingButton(true)}
              disabled={isAddingButton}
              className="flex items-center gap-2"
            >
              <Plus className="h-4 w-4" />
              Dodaj przycisk
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Formularz dodawania nowego przycisku */}
            {isAddingButton && (
              <div className="border rounded-lg p-4 bg-muted/50">
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="new-button-label">
                        Etykieta przycisku
                      </Label>
                      <Input
                        id="new-button-label"
                        placeholder="np. Serwer VPS"
                        value={newButton.label}
                        onChange={(e) =>
                          setNewButton({
                            ...newButton,
                            label: e.target.value,
                          })
                        }
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="new-button-key">Klucz metadata</Label>
                      <Input
                        id="new-button-key"
                        placeholder="np. server_type"
                        value={newButton.key}
                        onChange={(e) =>
                          setNewButton({
                            ...newButton,
                            key: e.target.value,
                          })
                        }
                      />
                    </div>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="new-button-value">Wartość</Label>
                      <Input
                        id="new-button-value"
                        placeholder="np. VPS"
                        value={newButton.value}
                        onChange={(e) =>
                          setNewButton({
                            ...newButton,
                            value: e.target.value,
                          })
                        }
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="new-button-icon">Ikona (Lucide)</Label>
                      <Select
                        value={newButton.icon}
                        onValueChange={(value) =>
                          setNewButton({
                            ...newButton,
                            icon: value,
                          })
                        }
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Wybierz ikonę">
                            {newButton.icon && (
                              <div className="flex items-center gap-2">
                                {renderIcon(newButton.icon)}
                                <span>{newButton.icon}</span>
                              </div>
                            )}
                          </SelectValue>
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Server">
                            <div className="flex items-center gap-2">
                              {renderIcon("Server")}
                              <span>Server</span>
                            </div>
                          </SelectItem>
                          <SelectItem value="Database">
                            <div className="flex items-center gap-2">
                              {renderIcon("Database")}
                              <span>Database</span>
                            </div>
                          </SelectItem>
                          <SelectItem value="Globe">
                            <div className="flex items-center gap-2">
                              {renderIcon("Globe")}
                              <span>Globe</span>
                            </div>
                          </SelectItem>
                          <SelectItem value="Shield">
                            <div className="flex items-center gap-2">
                              {renderIcon("Shield")}
                              <span>Shield</span>
                            </div>
                          </SelectItem>
                          <SelectItem value="Zap">
                            <div className="flex items-center gap-2">
                              {renderIcon("Zap")}
                              <span>Zap</span>
                            </div>
                          </SelectItem>
                          <SelectItem value="Settings">
                            <div className="flex items-center gap-2">
                              {renderIcon("Settings")}
                              <span>Settings</span>
                            </div>
                          </SelectItem>
                          <SelectItem value="Tag">
                            <div className="flex items-center gap-2">
                              {renderIcon("Tag")}
                              <span>Tag</span>
                            </div>
                          </SelectItem>
                          <SelectItem value="Star">
                            <div className="flex items-center gap-2">
                              {renderIcon("Star")}
                              <span>Star</span>
                            </div>
                          </SelectItem>
                          <SelectItem value="CheckCircle">
                            <div className="flex items-center gap-2">
                              {renderIcon("CheckCircle")}
                              <span>CheckCircle</span>
                            </div>
                          </SelectItem>
                          <SelectItem value="AlertCircle">
                            <div className="flex items-center gap-2">
                              {renderIcon("AlertCircle")}
                              <span>AlertCircle</span>
                            </div>
                          </SelectItem>
                          <SelectItem value="Code">
                            <div className="flex items-center gap-2">
                              {renderIcon("Code")}
                              <span>Code</span>
                            </div>
                          </SelectItem>
                          <SelectItem value="Lock">
                            <div className="flex items-center gap-2">
                              {renderIcon("Lock")}
                              <span>Lock</span>
                            </div>
                          </SelectItem>
                          <SelectItem value="Heart">
                            <div className="flex items-center gap-2">
                              {renderIcon("Heart")}
                              <span>Heart</span>
                            </div>
                          </SelectItem>
                          <SelectItem value="Home">
                            <div className="flex items-center gap-2">
                              {renderIcon("Home")}
                              <span>Home</span>
                            </div>
                          </SelectItem>
                          <SelectItem value="User">
                            <div className="flex items-center gap-2">
                              {renderIcon("User")}
                              <span>User</span>
                            </div>
                          </SelectItem>
                          <SelectItem value="Users">
                            <div className="flex items-center gap-2">
                              {renderIcon("Users")}
                              <span>Users</span>
                            </div>
                          </SelectItem>
                          <SelectItem value="Calendar">
                            <div className="flex items-center gap-2">
                              {renderIcon("Calendar")}
                              <span>Calendar</span>
                            </div>
                          </SelectItem>
                          <SelectItem value="FileText">
                            <div className="flex items-center gap-2">
                              {renderIcon("FileText")}
                              <span>FileText</span>
                            </div>
                          </SelectItem>
                          <SelectItem value="Image">
                            <div className="flex items-center gap-2">
                              {renderIcon("Image")}
                              <span>Image</span>
                            </div>
                          </SelectItem>
                          <SelectItem value="Search">
                            <div className="flex items-center gap-2">
                              {renderIcon("Search")}
                              <span>Search</span>
                            </div>
                          </SelectItem>
                          <SelectItem value="Bell">
                            <div className="flex items-center gap-2">
                              {renderIcon("Bell")}
                              <span>Bell</span>
                            </div>
                          </SelectItem>
                          <SelectItem value="MessageCircle">
                            <div className="flex items-center gap-2">
                              {renderIcon("MessageCircle")}
                              <span>MessageCircle</span>
                            </div>
                          </SelectItem>
                          <SelectItem value="Phone">
                            <div className="flex items-center gap-2">
                              {renderIcon("Phone")}
                              <span>Phone</span>
                            </div>
                          </SelectItem>
                          <SelectItem value="MapPin">
                            <div className="flex items-center gap-2">
                              {renderIcon("MapPin")}
                              <span>MapPin</span>
                            </div>
                          </SelectItem>
                          <SelectItem value="Link">
                            <div className="flex items-center gap-2">
                              {renderIcon("Link")}
                              <span>Link</span>
                            </div>
                          </SelectItem>
                          <SelectItem value="TrendingUp">
                            <div className="flex items-center gap-2">
                              {renderIcon("TrendingUp")}
                              <span>TrendingUp</span>
                            </div>
                          </SelectItem>
                          <SelectItem value="BarChart">
                            <div className="flex items-center gap-2">
                              {renderIcon("BarChart")}
                              <span>BarChart</span>
                            </div>
                          </SelectItem>
                          <SelectItem value="Activity">
                            <div className="flex items-center gap-2">
                              {renderIcon("Activity")}
                              <span>Activity</span>
                            </div>
                          </SelectItem>
                          <SelectItem value="Target">
                            <div className="flex items-center gap-2">
                              {renderIcon("Target")}
                              <span>Target</span>
                            </div>
                          </SelectItem>
                          <SelectItem value="Award">
                            <div className="flex items-center gap-2">
                              {renderIcon("Award")}
                              <span>Award</span>
                            </div>
                          </SelectItem>
                          <SelectItem value="ShoppingCart">
                            <div className="flex items-center gap-2">
                              {renderIcon("ShoppingCart")}
                              <span>ShoppingCart</span>
                            </div>
                          </SelectItem>
                          <SelectItem value="CreditCard">
                            <div className="flex items-center gap-2">
                              {renderIcon("CreditCard")}
                              <span>CreditCard</span>
                            </div>
                          </SelectItem>
                          <SelectItem value="DollarSign">
                            <div className="flex items-center gap-2">
                              {renderIcon("DollarSign")}
                              <span>DollarSign</span>
                            </div>
                          </SelectItem>
                          <SelectItem value="Briefcase">
                            <div className="flex items-center gap-2">
                              {renderIcon("Briefcase")}
                              <span>Briefcase</span>
                            </div>
                          </SelectItem>
                          <SelectItem value="Building">
                            <div className="flex items-center gap-2">
                              {renderIcon("Building")}
                              <span>Building</span>
                            </div>
                          </SelectItem>
                          <SelectItem value="Truck">
                            <div className="flex items-center gap-2">
                              {renderIcon("Truck")}
                              <span>Truck</span>
                            </div>
                          </SelectItem>
                          <SelectItem value="Coffee">
                            <div className="flex items-center gap-2">
                              {renderIcon("Coffee")}
                              <span>Coffee</span>
                            </div>
                          </SelectItem>
                          <SelectItem value="Lightbulb">
                            <div className="flex items-center gap-2">
                              {renderIcon("Lightbulb")}
                              <span>Lightbulb</span>
                            </div>
                          </SelectItem>
                          <SelectItem value="Sun">
                            <div className="flex items-center gap-2">
                              {renderIcon("Sun")}
                              <span>Sun</span>
                            </div>
                          </SelectItem>
                          <SelectItem value="Moon">
                            <div className="flex items-center gap-2">
                              {renderIcon("Moon")}
                              <span>Moon</span>
                            </div>
                          </SelectItem>
                          <SelectItem value="Cloud">
                            <div className="flex items-center gap-2">
                              {renderIcon("Cloud")}
                              <span>Cloud</span>
                            </div>
                          </SelectItem>
                          <SelectItem value="Key">
                            <div className="flex items-center gap-2">
                              {renderIcon("Key")}
                              <span>Key</span>
                            </div>
                          </SelectItem>
                          <SelectItem value="Mail">
                            <div className="flex items-center gap-2">
                              {renderIcon("Mail")}
                              <span>Mail</span>
                            </div>
                          </SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="new-button-description">
                      Opis (opcjonalny)
                    </Label>
                    <Textarea
                      id="new-button-description"
                      placeholder="Opis przycisku"
                      value={newButton.description}
                      onChange={(e) =>
                        setNewButton({
                          ...newButton,
                          description: e.target.value,
                        })
                      }
                    />
                  </div>
                  <div className="flex gap-2">
                    <Button onClick={handleAddButton} size="sm">
                      <Save className="h-4 w-4 mr-2" />
                      Zapisz
                    </Button>
                    <Button
                      onClick={cancelAddButton}
                      variant="outline"
                      size="sm"
                    >
                      <X className="h-4 w-4 mr-2" />
                      Anuluj
                    </Button>
                  </div>
                </div>
              </div>
            )}

            {/* Lista przycisków */}
            {isLoadingButtons ? (
              <div className="text-center py-8">
                <p className="text-muted-foreground">Ładowanie przycisków...</p>
              </div>
            ) : metadataButtons.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-muted-foreground">
                  Brak przycisków metadata. Dodaj pierwszy przycisk.
                </p>
              </div>
            ) : (
              <div className="space-y-2">
                {metadataButtons.map((button) => (
                  <div
                    key={button.id}
                    className="border rounded-lg p-4 flex items-center justify-between"
                  >
                    {editingButtonId === button.id ? (
                      <div className="flex-1 mr-4">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                          <div className="space-y-2">
                            <Label>Etykieta przycisku</Label>
                            <Input
                              value={editButton.label}
                              onChange={(e) =>
                                setEditButton({
                                  ...editButton,
                                  label: e.target.value,
                                })
                              }
                            />
                          </div>
                          <div className="space-y-2">
                            <Label>Klucz metadata</Label>
                            <Input
                              value={editButton.key}
                              onChange={(e) =>
                                setEditButton({
                                  ...editButton,
                                  key: e.target.value,
                                })
                              }
                            />
                          </div>
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                          <div className="space-y-2">
                            <Label>Wartość</Label>
                            <Input
                              value={editButton.value}
                              onChange={(e) =>
                                setEditButton({
                                  ...editButton,
                                  value: e.target.value,
                                })
                              }
                            />
                          </div>
                          <div className="space-y-2">
                            <Label>Ikona</Label>
                            <Select
                              value={editButton.icon}
                              onValueChange={(value) =>
                                setEditButton({
                                  ...editButton,
                                  icon: value,
                                })
                              }
                            >
                              <SelectTrigger>
                                <SelectValue>
                                  {editButton.icon && (
                                    <div className="flex items-center gap-2">
                                      {renderIcon(editButton.icon)}
                                      <span>{editButton.icon}</span>
                                    </div>
                                  )}
                                </SelectValue>
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="Server">
                                  <div className="flex items-center gap-2">
                                    {renderIcon("Server")}
                                    <span>Server</span>
                                  </div>
                                </SelectItem>
                                <SelectItem value="Database">
                                  <div className="flex items-center gap-2">
                                    {renderIcon("Database")}
                                    <span>Database</span>
                                  </div>
                                </SelectItem>
                                <SelectItem value="Globe">
                                  <div className="flex items-center gap-2">
                                    {renderIcon("Globe")}
                                    <span>Globe</span>
                                  </div>
                                </SelectItem>
                                <SelectItem value="Shield">
                                  <div className="flex items-center gap-2">
                                    {renderIcon("Shield")}
                                    <span>Shield</span>
                                  </div>
                                </SelectItem>
                                <SelectItem value="Zap">
                                  <div className="flex items-center gap-2">
                                    {renderIcon("Zap")}
                                    <span>Zap</span>
                                  </div>
                                </SelectItem>
                                <SelectItem value="Settings">
                                  <div className="flex items-center gap-2">
                                    {renderIcon("Settings")}
                                    <span>Settings</span>
                                  </div>
                                </SelectItem>
                                <SelectItem value="Tag">
                                  <div className="flex items-center gap-2">
                                    {renderIcon("Tag")}
                                    <span>Tag</span>
                                  </div>
                                </SelectItem>
                                <SelectItem value="Star">
                                  <div className="flex items-center gap-2">
                                    {renderIcon("Star")}
                                    <span>Star</span>
                                  </div>
                                </SelectItem>
                                <SelectItem value="CheckCircle">
                                  <div className="flex items-center gap-2">
                                    {renderIcon("CheckCircle")}
                                    <span>CheckCircle</span>
                                  </div>
                                </SelectItem>
                                <SelectItem value="AlertCircle">
                                  <div className="flex items-center gap-2">
                                    {renderIcon("AlertCircle")}
                                    <span>AlertCircle</span>
                                  </div>
                                </SelectItem>
                                <SelectItem value="Code">
                                  <div className="flex items-center gap-2">
                                    {renderIcon("Code")}
                                    <span>Code</span>
                                  </div>
                                </SelectItem>
                                <SelectItem value="Lock">
                                  <div className="flex items-center gap-2">
                                    {renderIcon("Lock")}
                                    <span>Lock</span>
                                  </div>
                                </SelectItem>
                                <SelectItem value="Heart">
                                  <div className="flex items-center gap-2">
                                    {renderIcon("Heart")}
                                    <span>Heart</span>
                                  </div>
                                </SelectItem>
                                <SelectItem value="Home">
                                  <div className="flex items-center gap-2">
                                    {renderIcon("Home")}
                                    <span>Home</span>
                                  </div>
                                </SelectItem>
                                <SelectItem value="User">
                                  <div className="flex items-center gap-2">
                                    {renderIcon("User")}
                                    <span>User</span>
                                  </div>
                                </SelectItem>
                                <SelectItem value="Users">
                                  <div className="flex items-center gap-2">
                                    {renderIcon("Users")}
                                    <span>Users</span>
                                  </div>
                                </SelectItem>
                                <SelectItem value="Calendar">
                                  <div className="flex items-center gap-2">
                                    {renderIcon("Calendar")}
                                    <span>Calendar</span>
                                  </div>
                                </SelectItem>
                                <SelectItem value="FileText">
                                  <div className="flex items-center gap-2">
                                    {renderIcon("FileText")}
                                    <span>FileText</span>
                                  </div>
                                </SelectItem>
                                <SelectItem value="Image">
                                  <div className="flex items-center gap-2">
                                    {renderIcon("Image")}
                                    <span>Image</span>
                                  </div>
                                </SelectItem>
                                <SelectItem value="Search">
                                  <div className="flex items-center gap-2">
                                    {renderIcon("Search")}
                                    <span>Search</span>
                                  </div>
                                </SelectItem>
                                <SelectItem value="Bell">
                                  <div className="flex items-center gap-2">
                                    {renderIcon("Bell")}
                                    <span>Bell</span>
                                  </div>
                                </SelectItem>
                                <SelectItem value="MessageCircle">
                                  <div className="flex items-center gap-2">
                                    {renderIcon("MessageCircle")}
                                    <span>MessageCircle</span>
                                  </div>
                                </SelectItem>
                                <SelectItem value="Phone">
                                  <div className="flex items-center gap-2">
                                    {renderIcon("Phone")}
                                    <span>Phone</span>
                                  </div>
                                </SelectItem>
                                <SelectItem value="MapPin">
                                  <div className="flex items-center gap-2">
                                    {renderIcon("MapPin")}
                                    <span>MapPin</span>
                                  </div>
                                </SelectItem>
                                <SelectItem value="Link">
                                  <div className="flex items-center gap-2">
                                    {renderIcon("Link")}
                                    <span>Link</span>
                                  </div>
                                </SelectItem>
                                <SelectItem value="TrendingUp">
                                  <div className="flex items-center gap-2">
                                    {renderIcon("TrendingUp")}
                                    <span>TrendingUp</span>
                                  </div>
                                </SelectItem>
                                <SelectItem value="BarChart">
                                  <div className="flex items-center gap-2">
                                    {renderIcon("BarChart")}
                                    <span>BarChart</span>
                                  </div>
                                </SelectItem>
                                <SelectItem value="Activity">
                                  <div className="flex items-center gap-2">
                                    {renderIcon("Activity")}
                                    <span>Activity</span>
                                  </div>
                                </SelectItem>
                                <SelectItem value="Target">
                                  <div className="flex items-center gap-2">
                                    {renderIcon("Target")}
                                    <span>Target</span>
                                  </div>
                                </SelectItem>
                                <SelectItem value="Award">
                                  <div className="flex items-center gap-2">
                                    {renderIcon("Award")}
                                    <span>Award</span>
                                  </div>
                                </SelectItem>
                                <SelectItem value="ShoppingCart">
                                  <div className="flex items-center gap-2">
                                    {renderIcon("ShoppingCart")}
                                    <span>ShoppingCart</span>
                                  </div>
                                </SelectItem>
                                <SelectItem value="CreditCard">
                                  <div className="flex items-center gap-2">
                                    {renderIcon("CreditCard")}
                                    <span>CreditCard</span>
                                  </div>
                                </SelectItem>
                                <SelectItem value="DollarSign">
                                  <div className="flex items-center gap-2">
                                    {renderIcon("DollarSign")}
                                    <span>DollarSign</span>
                                  </div>
                                </SelectItem>
                                <SelectItem value="Briefcase">
                                  <div className="flex items-center gap-2">
                                    {renderIcon("Briefcase")}
                                    <span>Briefcase</span>
                                  </div>
                                </SelectItem>
                                <SelectItem value="Building">
                                  <div className="flex items-center gap-2">
                                    {renderIcon("Building")}
                                    <span>Building</span>
                                  </div>
                                </SelectItem>
                                <SelectItem value="Truck">
                                  <div className="flex items-center gap-2">
                                    {renderIcon("Truck")}
                                    <span>Truck</span>
                                  </div>
                                </SelectItem>
                                <SelectItem value="Coffee">
                                  <div className="flex items-center gap-2">
                                    {renderIcon("Coffee")}
                                    <span>Coffee</span>
                                  </div>
                                </SelectItem>
                                <SelectItem value="Lightbulb">
                                  <div className="flex items-center gap-2">
                                    {renderIcon("Lightbulb")}
                                    <span>Lightbulb</span>
                                  </div>
                                </SelectItem>
                                <SelectItem value="Sun">
                                  <div className="flex items-center gap-2">
                                    {renderIcon("Sun")}
                                    <span>Sun</span>
                                  </div>
                                </SelectItem>
                                <SelectItem value="Moon">
                                  <div className="flex items-center gap-2">
                                    {renderIcon("Moon")}
                                    <span>Moon</span>
                                  </div>
                                </SelectItem>
                                <SelectItem value="Cloud">
                                  <div className="flex items-center gap-2">
                                    {renderIcon("Cloud")}
                                    <span>Cloud</span>
                                  </div>
                                </SelectItem>
                                <SelectItem value="Key">
                                  <div className="flex items-center gap-2">
                                    {renderIcon("Key")}
                                    <span>Key</span>
                                  </div>
                                </SelectItem>
                                <SelectItem value="Mail">
                                  <div className="flex items-center gap-2">
                                    {renderIcon("Mail")}
                                    <span>Mail</span>
                                  </div>
                                </SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                        </div>
                        <div className="space-y-2">
                          <Label>Opis</Label>
                          <Textarea
                            value={editButton.description}
                            onChange={(e) =>
                              setEditButton({
                                ...editButton,
                                description: e.target.value,
                              })
                            }
                          />
                        </div>
                      </div>
                    ) : (
                      <div className="flex-1">
                        <div className="flex items-center gap-2">
                          <div className="text-muted-foreground">
                            {renderIcon(button.icon)}
                          </div>
                          <h3 className="font-medium">{button.label}</h3>
                          <span className="text-xs bg-muted px-2 py-1 rounded">
                            {button.key}
                          </span>
                        </div>
                        <p className="text-sm text-muted-foreground mt-1">
                          Wartość:{" "}
                          <code className="bg-muted px-1 rounded">
                            {button.value.toString()}
                          </code>
                        </p>
                        {button.description && (
                          <p className="text-sm text-muted-foreground mt-1">
                            {button.description}
                          </p>
                        )}
                      </div>
                    )}

                    <div className="flex gap-2">
                      {editingButtonId === button.id ? (
                        <>
                          <Button
                            onClick={() => handleEditButton(button.id)}
                            size="sm"
                            variant="default"
                          >
                            <Save className="h-4 w-4" />
                          </Button>
                          <Button
                            onClick={cancelEditButton}
                            size="sm"
                            variant="outline"
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </>
                      ) : (
                        <>
                          <Button
                            onClick={() => startEditButton(button)}
                            size="sm"
                            variant="outline"
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            onClick={() => handleDeleteButton(button.id)}
                            size="sm"
                            variant="destructive"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </CardContent>
      </Card>

    </div>
  );
}
