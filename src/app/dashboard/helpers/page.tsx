"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";
import {
  Loader2,
  CheckCircle,
  AlertCircle,
  Globe,
  Plus,
  Download,
  Trash2,
  RefreshCw,
} from "lucide-react";

interface CreateDomainsResult {
  domain: string;
  fullDomain: string;
  category: string;
  linkCount: number;
  cms: string;
  status: string;
  error?: string;
}

interface CreateDomainsResponse {
  success: boolean;
  message: string;
  processed: number;
  created: number;
  skipped: number;
  results: CreateDomainsResult[];
  summary: {
    wordpress: number;
    inny: number;
  };
}

interface DomainExportData {
  id: string;
  domain: string;
  fullDomain: string;
  category: string;
  linkCount: number;
  cms: string;
  createdAt: string;
}

export default function HelpersPage() {
  // Stan dla tworzenia domen z linków
  const [isCreatingDoma<PERSON>, setIsCreatingDomains] = useState(false);
  const [lastCreateDomainsTime, setLastCreateDomainsTime] = useState<
    string | null
  >(null);
  const [createDomainsStatus, setCreateDomainsStatus] = useState<
    "idle" | "success" | "error"
  >("idle");
  const [createDomainsResults, setCreateDomainsResults] = useState<
    CreateDomainsResult[]
  >([]);
  const [lastCreateDuration, setLastCreateDuration] = useState<number | null>(
    null
  );

  // Stan dla eksportu CSV
  const [isExportingCsv, setIsExportingCsv] = useState(false);

  // Stan dla usuwania wszystkich domen
  const [isDeletingAllDomains, setIsDeletingAllDomains] = useState(false);

  // Stan dla czyszczenia danych wszystkich domen
  const [isClearingAllDomains, setIsClearingAllDomains] = useState(false);

  const createDomainsFromLinks = async () => {
    const startTime = Date.now();
    setIsCreatingDomains(true);
    setCreateDomainsStatus("idle");
    setCreateDomainsResults([]);

    try {
      const response = await fetch("/api/domains/create-from-links", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data: CreateDomainsResponse = await response.json();

      if (data.success) {
        const endTime = Date.now();
        const duration = Math.round((endTime - startTime) / 1000); // w sekundach

        setCreateDomainsStatus("success");
        setCreateDomainsResults(data.results);
        setLastCreateDomainsTime(new Date().toLocaleString("pl-PL"));
        setLastCreateDuration(duration);
        toast.success(
          `${data.message}. WordPress: ${data.summary.wordpress}, Inne: ${data.summary.inny}`
        );
      } else {
        throw new Error("API returned success: false");
      }
    } catch (error) {
      console.error("Błąd tworzenia domen:", error);
      setCreateDomainsStatus("error");
      toast.error(
        `Błąd tworzenia domen: ${
          error instanceof Error ? error.message : "Nieznany błąd"
        }`
      );
    } finally {
      setIsCreatingDomains(false);
    }
  };

  const exportNewDomainsToCSV = async () => {
    setIsExportingCsv(true);

    try {
      const response = await fetch("/api/domains/export-new", {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (data.success && data.data.length > 0) {
        // Konwertuj dane do CSV
        const csvHeaders = [
          "ID",
          "Domena",
          "Pełna domena",
          "Kategoria",
          "Liczba linków",
          "CMS",
          "Data utworzenia",
        ];
        const csvRows = data.data.map((domain: DomainExportData) => [
          domain.id,
          domain.domain,
          domain.fullDomain,
          domain.category,
          domain.linkCount,
          domain.cms || "",
          new Date(domain.createdAt).toLocaleString("pl-PL"),
        ]);

        // Utwórz zawartość CSV
        const csvContent = [
          csvHeaders.join(","),
          ...csvRows.map((row: string[]) =>
            row.map((field) => `"${field}"`).join(",")
          ),
        ].join("\n");

        // Utwórz i pobierz plik
        const blob = new Blob([csvContent], {
          type: "text/csv;charset=utf-8;",
        });
        const link = document.createElement("a");
        const url = URL.createObjectURL(blob);
        link.setAttribute("href", url);
        link.setAttribute(
          "download",
          `domeny_nowe_${new Date().toISOString().split("T")[0]}.csv`
        );
        link.style.visibility = "hidden";
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        toast.success(`Wyeksportowano ${data.count} domen do pliku CSV`);
      } else {
        toast.info('Brak domen ze statusem "nowe" do eksportu');
      }
    } catch (error) {
      console.error("Błąd eksportu CSV:", error);
      toast.error(
        `Błąd eksportu CSV: ${
          error instanceof Error ? error.message : "Nieznany błąd"
        }`
      );
    } finally {
      setIsExportingCsv(false);
    }
  };

  const deleteAllDomains = async () => {
    // Potwierdzenie przed usunięciem
    if (
      !confirm(
        "Czy na pewno chcesz usunąć WSZYSTKIE domeny? Ta operacja jest nieodwracalna!"
      )
    ) {
      return;
    }

    setIsDeletingAllDomains(true);

    try {
      const response = await fetch("/api/domains", {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (data.success) {
        toast.success(data.message);
      } else {
        throw new Error("API returned success: false");
      }
    } catch (error) {
      console.error("Błąd usuwania wszystkich domen:", error);
      toast.error(
        `Błąd usuwania wszystkich domen: ${
          error instanceof Error ? error.message : "Nieznany błąd"
        }`
      );
    } finally {
      setIsDeletingAllDomains(false);
    }
  };

  const clearAllDomainsData = async () => {
    // Potwierdzenie przed wyczyszczeniem
    if (
      !confirm(
        "Czy na pewno chcesz wyczyścić dane WSZYSTKICH domen (metadata, contact_data, auditContent) i ustawić status na 'new'? Ta operacja jest nieodwracalna!"
      )
    ) {
      return;
    }

    setIsClearingAllDomains(true);

    try {
      const response = await fetch("/api/domains/clear-all-data", {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (data.success) {
        toast.success(`${data.message} Wyczyszczono ${data.count} domen.`);
      } else {
        throw new Error("API returned success: false");
      }
    } catch (error) {
      console.error("Błąd czyszczenia danych wszystkich domen:", error);
      toast.error(
        `Błąd czyszczenia danych wszystkich domen: ${
          error instanceof Error ? error.message : "Nieznany błąd"
        }`
      );
    } finally {
      setIsClearingAllDomains(false);
    }
  };

  const getCreateDomainsStatusIcon = () => {
    switch (createDomainsStatus) {
      case "success":
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case "error":
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      default:
        return null;
    }
  };

  const getCreateDomainsStatusText = () => {
    switch (createDomainsStatus) {
      case "success":
        return "Ostatnie tworzenie domen: sukces";
      case "error":
        return "Ostatnie tworzenie domen: błąd";
      default:
        return "Nie tworzono jeszcze domen z linków";
    }
  };

  const formatDuration = (seconds: number) => {
    if (seconds < 60) {
      return `${seconds}s`;
    } else if (seconds < 3600) {
      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = seconds % 60;
      return `${minutes}m ${remainingSeconds}s`;
    } else {
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      return `${hours}h ${minutes}m`;
    }
  };

  return (
    <div className="space-y-6">
      {/* Nagłówek */}
      <div>
        <h1 className="text-3xl font-bold text-foreground">Helpers</h1>
        <p className="text-muted-foreground mt-2">
          Narzędzia pomocnicze do zarządzania danymi. Ta strona jest tymczasowa
          i zostanie usunięta w przyszłości.
        </p>
      </div>

      {/* Tworzenie domen z linków */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Plus className="h-5 w-5" />
            Tworzenie domen z linków
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-medium">
                  Utwórz domeny z istniejących linków
                </h3>
                <p className="text-sm text-muted-foreground">
                  Przeanalizuj wszystkie linki w bazie i utwórz domeny dla tych,
                  które jeszcze nie istnieją
                </p>
              </div>
              <Button
                onClick={createDomainsFromLinks}
                disabled={isCreatingDomains}
                className="flex items-center gap-2"
              >
                {isCreatingDomains ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <Plus className="h-4 w-4" />
                )}
                {isCreatingDomains ? "Tworzenie..." : "Utwórz domeny"}
              </Button>
            </div>

            <div className="flex items-center gap-2 text-sm">
              {getCreateDomainsStatusIcon()}
              <span
                className={
                  createDomainsStatus === "error"
                    ? "text-red-500"
                    : createDomainsStatus === "success"
                    ? "text-green-500"
                    : "text-muted-foreground"
                }
              >
                {getCreateDomainsStatusText()}
              </span>
            </div>

            {lastCreateDomainsTime && (
              <div className="text-sm text-muted-foreground">
                Ostatnie tworzenie: {lastCreateDomainsTime}
                {lastCreateDuration && (
                  <span className="ml-2">
                    (czas: {formatDuration(lastCreateDuration)})
                  </span>
                )}
              </div>
            )}

            {/* Podsumowanie ostatniego tworzenia */}
            {createDomainsStatus === "success" &&
              createDomainsResults.length > 0 && (
                <div className="mt-4 p-4 bg-muted/50 rounded-lg border">
                  <h4 className="font-medium mb-3">
                    Podsumowanie ostatniego tworzenia
                  </h4>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-primary">
                        {createDomainsResults.length}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        Przetworzonych domen
                      </div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-green-600">
                        {
                          createDomainsResults.filter(
                            (r) => r.status === "created"
                          ).length
                        }
                      </div>
                      <div className="text-sm text-muted-foreground">
                        Utworzonych
                      </div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-blue-600">
                        {
                          createDomainsResults.filter(
                            (r) => r.cms === "wordpress"
                          ).length
                        }
                      </div>
                      <div className="text-sm text-muted-foreground">
                        WordPress
                      </div>
                    </div>
                    {lastCreateDuration && (
                      <div className="text-center">
                        <div className="text-2xl font-bold text-green-600">
                          {formatDuration(lastCreateDuration)}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          Czas trwania
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Statystyki błędów */}
                  {createDomainsResults.some((r) => r.status === "error") && (
                    <div className="mt-3 pt-3 border-t">
                      <div className="flex items-center gap-2 text-sm text-amber-600">
                        <AlertCircle className="h-4 w-4" />
                        <span>
                          {
                            createDomainsResults.filter(
                              (r) => r.status === "error"
                            ).length
                          }{" "}
                          domen z błędami
                        </span>
                      </div>
                    </div>
                  )}
                </div>
              )}

            {/* Komunikat o braku linków */}
            {createDomainsStatus === "success" &&
              createDomainsResults.length === 0 && (
                <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-lg">
                  <div className="flex items-center gap-2 text-green-800">
                    <CheckCircle className="h-4 w-4" />
                    <span className="font-medium">
                      Brak linków do przetworzenia
                    </span>
                  </div>
                  <p className="text-sm text-green-700 mt-1">
                    Nie znaleziono linków w bazie danych.
                  </p>
                </div>
              )}
          </div>

          {/* Wyniki tworzenia domen */}
          {createDomainsResults.length > 0 && (
            <div className="border-t pt-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium">
                  Wyniki tworzenia ({createDomainsResults.length})
                </h3>
                <div className="flex gap-2">
                  <Badge variant="secondary">
                    Utworzonych:{" "}
                    {
                      createDomainsResults.filter((r) => r.status === "created")
                        .length
                    }
                  </Badge>
                  <Badge variant="outline">
                    Pominiętych:{" "}
                    {
                      createDomainsResults.filter((r) => r.status === "skipped")
                        .length
                    }
                  </Badge>
                </div>
              </div>

              <div className="space-y-2 max-h-60 overflow-y-auto">
                {createDomainsResults.map((result, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between p-3 border rounded-lg"
                  >
                    <div className="flex items-center gap-3">
                      <Globe className="h-4 w-4 text-muted-foreground" />
                      <div>
                        <div className="font-medium">{result.domain}</div>
                        <div className="text-sm text-muted-foreground">
                          {result.category} • {result.linkCount} linków
                          {result.error && ` • ${result.error}`}
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge
                        variant={
                          result.cms === "wordpress" ? "default" : "outline"
                        }
                        className={
                          result.cms === "wordpress" ? "bg-blue-500" : ""
                        }
                      >
                        {result.cms === "wordpress"
                          ? "WordPress"
                          : result.cms === "inny"
                          ? "Inne"
                          : "Nieznany"}
                      </Badge>
                      {result.status === "created" ? (
                        <CheckCircle className="h-4 w-4 text-green-500" />
                      ) : result.status === "skipped" ? (
                        <AlertCircle className="h-4 w-4 text-yellow-500" />
                      ) : (
                        <AlertCircle className="h-4 w-4 text-red-500" />
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Eksport domen do CSV */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Download className="h-5 w-5" />
            Eksport domen do CSV
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-medium">
                Pobierz domeny ze statusem nowe
              </h3>
              <p className="text-sm text-muted-foreground">
                Eksportuj wszystkie domeny ze statusem nowe wraz z ich ID do
                pliku CSV
              </p>
            </div>
            <Button
              onClick={exportNewDomainsToCSV}
              disabled={isExportingCsv}
              className="flex items-center gap-2"
            >
              {isExportingCsv ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Download className="h-4 w-4" />
              )}
              {isExportingCsv ? "Eksportowanie..." : "Pobierz CSV"}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Czyszczenie danych wszystkich domen */}
      <Card className="border-orange-200">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-orange-600">
            <RefreshCw className="h-5 w-5" />
            Czyszczenie danych wszystkich domen
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-medium text-orange-600">
                Wyczyść dane wszystkich domen
              </h3>
              <p className="text-sm text-muted-foreground">
                <strong className="text-orange-600">UWAGA:</strong> Ta operacja
                wyczyści dla WSZYSTKICH domen: metadata, contact_data,
                auditContent i ustawi status na "new". Operacja jest
                nieodwracalna!
              </p>
            </div>
            <Button
              onClick={clearAllDomainsData}
              disabled={isClearingAllDomains}
              variant="outline"
              className="flex items-center gap-2 border-orange-200 text-orange-600 hover:bg-orange-50"
            >
              {isClearingAllDomains ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <RefreshCw className="h-4 w-4" />
              )}
              {isClearingAllDomains ? "Czyszczenie..." : "Wyczyść dane"}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Usuwanie wszystkich domen */}
      <Card className="border-red-200">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-red-600">
            <Trash2 className="h-5 w-5" />
            Usuwanie wszystkich domen
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-medium text-red-600">
                Usuń wszystkie domeny
              </h3>
              <p className="text-sm text-muted-foreground">
                <strong className="text-red-600">UWAGA:</strong> Ta operacja
                usunie WSZYSTKIE domeny z bazy danych. Operacja jest
                nieodwracalna!
              </p>
            </div>
            <Button
              onClick={deleteAllDomains}
              disabled={isDeletingAllDomains}
              variant="destructive"
              className="flex items-center gap-2"
            >
              {isDeletingAllDomains ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Trash2 className="h-4 w-4" />
              )}
              {isDeletingAllDomains ? "Usuwanie..." : "Usuń wszystkie"}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
