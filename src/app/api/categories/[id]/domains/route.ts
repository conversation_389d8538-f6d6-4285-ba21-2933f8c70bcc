import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { Session } from 'next-auth';
import { authOptions } from '@/lib/auth';
import connectDB from '@/lib/db';
import Domain from '@/models/Domain';
import Category from '@/models/Category';

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = (await getServerSession(authOptions)) as Session | null;

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    await connectDB();

    // Znajdź kategorię żeby pobrać jej nazwę
    const category = await Category.findOne({
      _id: params.id,
      userId: session.user.id
    });

    if (!category) {
      return NextResponse.json(
        { error: 'Kategoria nie została znaleziona' },
        { status: 404 }
      );
    }

    // Usuń wszystkie domeny z tej kategorii
    const result = await Domain.deleteMany({
      userId: session.user.id,
      category: category.name
    });

    return NextResponse.json({
      success: true,
      message: `Usunięto ${result.deletedCount} domen z kategorii "${category.name}"`,
      deletedCount: result.deletedCount,
      categoryName: category.name
    });

  } catch (error) {
    console.error('Błąd usuwania domen z kategorii:', error);
    return NextResponse.json(
      { error: 'Wystąpił błąd podczas usuwania domen z kategorii' },
      { status: 500 }
    );
  }
}