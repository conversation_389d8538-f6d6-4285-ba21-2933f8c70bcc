import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/db';
import Vulnerability from '@/models/Vulnerability';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ vulnerability_id: string }> }
) {
  try {
    await connectDB();
    
    const { vulnerability_id } = await params;

    const vulnerability = await Vulnerability.findOne({
      vulnerability_id: vulnerability_id
    });

    if (!vulnerability) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Podatność nie została znaleziona' 
        },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      vulnerability: {
        vulnerability_id: vulnerability.vulnerability_id,
        vulnerability_name: vulnerability.vulnerability_name,
        volnerability_description: vulnerability.volnerability_description,
        createdAt: vulnerability.createdAt,
        updatedAt: vulnerability.updatedAt
      }
    });

  } catch (error) {
    console.error('Błąd pobierania podatności:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Wys<PERSON><PERSON><PERSON><PERSON> błąd podczas pobierania podatności' 
      },
      { status: 500 }
    );
  }
}