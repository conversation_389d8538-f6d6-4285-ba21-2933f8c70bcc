import { NextResponse } from 'next/server';
import connectDB from '@/lib/db';
import Category from '@/models/Category';

export async function GET() {
  try {
    await connectDB();

    // Pobierz wszystkie kategorie ze wszystkich użytkowników
    const categories = await Category.find({})
      .sort({ name: 1 })
      .select('name description createdAt updatedAt');

    return NextResponse.json({
      success: true,
      categories: categories.map(category => ({
        id: category._id,
        name: category.name,
        description: category.description,
        createdAt: category.createdAt,
        updatedAt: category.updatedAt
      }))
    });

  } catch (error) {
    console.error('Błąd pobierania kategorii:', error);
    return NextResponse.json(
      { error: 'Wystąpił błąd podczas pobierania kategorii' },
      { status: 500 }
    );
  }
}