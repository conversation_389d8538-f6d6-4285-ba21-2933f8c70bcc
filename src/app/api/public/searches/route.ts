import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/db';
import SearchQuery from '@/models/SearchQuery';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const category = searchParams.get('category');

    if (!category) {
      return NextResponse.json(
        { error: 'Parametr category jest wymagany' },
        { status: 400 }
      );
    }

    await connectDB();

    // Pobierz wszystkie wyszukiwania dla podanej kategorii
    const searches = await SearchQuery.find({ category: category.trim() })
      .sort({ createdAt: -1 })
      .select('query category location page createdAt updatedAt');

    return NextResponse.json({
      success: true,
      category: category.trim(),
      count: searches.length,
      searches: searches.map(search => ({
        id: search._id,
        query: search.query,
        category: search.category,
        location: search.location,
        page: search.page,
        createdAt: search.createdAt,
        updatedAt: search.updatedAt
      }))
    });

  } catch (error) {
    console.error('Błąd pobierania wyszukiwań:', error);
    return NextResponse.json(
      { error: 'Wystą<PERSON>ł błąd podczas pobierania wyszukiwań' },
      { status: 500 }
    );
  }
}