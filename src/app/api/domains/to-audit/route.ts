import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/db';
import Domain from '@/models/Domain';

interface ToAuditFilter {
  status: { $in: string[] };
  cms: string;
  category?: { $regex: RegExp };
  _id?: string;
}

export async function GET(request: NextRequest) {
  try {
    await connectDB();

    const { searchParams } = new URL(request.url);
    const limitParam = searchParams.get('limit');
    const categoryParam = searchParams.get('category');
    const idParam = searchParams.get('id');

    // Jeśli nie podano limitu, zwróć wszystkie domeny
    const limit = limitParam ? parseInt(limitParam) : null;

    // Znajdź domeny które spełniają warunki:
    // - status 'new' lub 'in_audit'
    // - cms ustawiony na 'wordpress'
    // - opcjonalnie filtruj po kategorii
    // - publiczny dostęp do wszystkich domen WordPress
    const filter: ToAuditFilter = {
      status: { $in: ['new', 'in_audit'] },
      cms: 'wordpress'
    };

    // Dodaj filtr kategorii jeśli został podany (case-insensitive)
    if (categoryParam) {
      filter.category = { $regex: new RegExp(`^${categoryParam}$`, 'i') };
    }

    // Dodaj filtr ID jeśli został podany
    if (idParam) {
      filter._id = idParam;
    }

    // Buduj zapytanie (bez pobierania treści audytu i lighthouse)
    let query = Domain.find(filter)
      .sort({ createdAt: -1 }) // sortuj po dacie utworzenia (najstarsze pierwsze)
      .select('_id fullDomain domain protocol category cms status linkCount metadata createdAt updatedAt');

    // Zastosuj limit jeśli został podany
    if (limit && limit > 0) {
      query = query.limit(limit);
    }

    // Pobierz domeny
    const domains = await query;

    // Dodaj metadata do domen które nie mają jeszcze ustawionych pól audytu
    const currentDate = new Date();
    const domainsToUpdate = domains.filter(domain =>
      !domain.metadata?.hasAudit ||
      !domain.metadata?.dateAudit
    );

    if (domainsToUpdate.length > 0) {
      // Aktualizuj domeny które nie mają jeszcze metadata audytu
      const updatePromises = domainsToUpdate.map(domain =>
        Domain.findByIdAndUpdate(domain._id, {
          metadata: {
            ...domain.metadata,
            hasAudit: true,
            dateAudit: currentDate.toISOString()
          }
        }, { new: true })
      );

      await Promise.all(updatePromises);
    }

    // Pobierz zaktualizowane domeny z tym samym zapytaniem co wcześniej
    let updatedQuery = Domain.find(filter)
      .sort({ createdAt: -1 })
      .select('_id fullDomain domain protocol category cms status linkCount metadata createdAt updatedAt');

    if (limit && limit > 0) {
      updatedQuery = updatedQuery.limit(limit);
    }

    const updatedDomains = await updatedQuery;

    // Policz całkowitą liczbę domen spełniających warunki
    const totalCount = await Domain.countDocuments(filter);

    // Formatuj odpowiedź zgodnie ze strukturą bazy danych (bez treści audytu i lighthouse)
    const formattedDomains = updatedDomains.map(domain => ({
      id: domain._id,
      domain: domain.domain,
      protocol: domain.protocol,
      fullDomain: domain.fullDomain,
      category: domain.category,
      cms: domain.cms,
      ocena: domain.ocena,
      status: domain.status,
      linkCount: domain.linkCount,
      metadata: domain.metadata,
      createdAt: domain.createdAt,
      updatedAt: domain.updatedAt
    }));

    // Przygotuj wiadomość
    const returnedCount = formattedDomains.length;
    let message: string;
    const categoryText = categoryParam ? ` w kategorii "${categoryParam}"` : '';

    if (idParam) {
      // Jeśli szukano po ID
      if (returnedCount === 0) {
        message = `Nie znaleziono domeny WordPress o ID "${idParam}" do audytu bezpieczeństwa`;
      } else {
        message = `Znaleziono domenę WordPress o ID "${idParam}" do audytu bezpieczeństwa`;
      }
    } else {
      // Standardowe wyszukiwanie
      if (returnedCount === 0) {
        message = `Brak domen WordPress do audytu bezpieczeństwa${categoryText}`;
      } else if (limit && returnedCount === limit) {
        message = `Znaleziono ${returnedCount} z ${totalCount} domen WordPress do audytu bezpieczeństwa${categoryText}`;
      } else {
        message = `Znaleziono ${returnedCount} domen WordPress do audytu bezpieczeństwa${categoryText}`;
      }
    }

    return NextResponse.json({
      success: true,
      domains: formattedDomains,
      count: returnedCount,
      totalCount,
      message
    });

  } catch (error) {
    console.error('Błąd pobierania domen do audytu:', error);
    return NextResponse.json(
      { error: 'Wystąpił błąd podczas pobierania domen do audytu' },
      { status: 500 }
    );
  }
}
