import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { Session } from 'next-auth';
import { authOptions } from '@/lib/auth';
import connectDB from '@/lib/db';
import Domain from '@/models/Domain';

export async function PUT(request: NextRequest) {
  try {
    const session = (await getServerSession(authOptions)) as Session | null;

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    await connectDB();

    // Aktualizuj wszystkie domeny - wyczyść dane i ustaw status na 'new'
    const updateResult = await Domain.updateMany(
      {}, // Wszystkie domeny
      {
        $unset: {
          metadata: 1,
          contact_data: 1,
          auditContent: 1
        },
        $set: {
          status: 'new'
        }
      }
    );

    console.log(`Wyczyszczono dane dla ${updateResult.modifiedCount} domen`);

    return NextResponse.json({
      success: true,
      message: '<PERSON><PERSON><PERSON><PERSON><PERSON> wyczyszczono dane wszystkich domen.',
      count: updateResult.modifiedCount
    });

  } catch (error) {
    console.error('Błąd czyszczenia danych wszystkich domen:', error);
    return NextResponse.json(
      { error: 'Wystąpił błąd podczas czyszczenia danych wszystkich domen' },
      { status: 500 }
    );
  }
}