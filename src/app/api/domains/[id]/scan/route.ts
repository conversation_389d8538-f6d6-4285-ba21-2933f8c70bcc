import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { Session } from 'next-auth';
import { authOptions } from '@/lib/auth';
import connectDB from '@/lib/db';
import Domain from '@/models/Domain';
import { MongoClient } from 'mongodb';

export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = (await getServerSession(authOptions)) as Session | null;

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    await connectDB();

    const { id } = await params;

    // Sprawdź czy domena istnieje i należy do użytkownika
    const domain = await Domain.findOne({
      _id: id,
      userId: session.user.id
    });

    if (!domain) {
      return NextResponse.json(
        { error: 'Domena nie została znaleziona' },
        { status: 404 }
      );
    }

    // Połącz bezpośrednio z MongoDB aby uzyskać dostęp do kolekcji 'scans'
    const MONGODB_URI = process.env.MONGODB_URI;
    if (!MONGODB_URI) {
      throw new Error('MONGODB_URI not configured');
    }

    const client = new MongoClient(MONGODB_URI);
    await client.connect();

    try {
      // Pobierz najnowszy skan dla tej domeny
      const db = client.db('test'); // Bazując na main.py, dane są w bazie 'test'
      const scansCollection = db.collection('scans');

      // Znajdź najnowszy skan dla tego domain_id
      const scanData = await scansCollection.findOne(
        { domain_id: id },
        { sort: { timestamp: -1 } } // Najnowszy skan
      );

      if (!scanData) {
        return NextResponse.json({
          success: false,
          message: 'Brak danych skanu dla tej domeny'
        });
      }

      // Usuń _id z MongoDB przed zwróceniem
      const { _id: mongoId, ...cleanScanData } = scanData;

      return NextResponse.json({
        success: true,
        data: cleanScanData
      });

    } finally {
      await client.close();
    }

  } catch (error) {
    console.error('Błąd pobierania danych skanu:', error);
    return NextResponse.json(
      { error: 'Wystąpił błąd podczas pobierania danych skanu' },
      { status: 500 }
    );
  }
}