import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { Session } from 'next-auth';
import { authOptions } from '@/lib/auth';
import connectDB from '@/lib/db';
import Domain from '@/models/Domain';

// POST endpoint do wykonywania audytu domeny
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = (await getServerSession(authOptions)) as Session | null;

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    await connectDB();

    const { id } = await params;

    // Sprawdź czy domena istnieje i należy do użytkownika
    const domain = await Domain.findOne({
      _id: id,
      userId: session.user.id
    });

    if (!domain) {
      return NextResponse.json(
        {
          success: false,
          error: 'Domain not found or access denied'
        },
        { status: 404 }
      );
    }

    // Sprawdź czy domena już ma audyt
    if (domain.auditContent && domain.auditContent.trim().length > 0) {
      return NextResponse.json(
        {
          success: false,
          error: 'Domain already has an audit'
        },
        { status: 400 }
      );
    }

    // Przygotuj dane do wysłania na webhook
    const webhookData = {
      domainId: domain._id.toString(),
      domain: domain.domain,
      fullDomain: domain.fullDomain,
      cms: domain.cms,
      protocol: domain.protocol,
      category: domain.category,
      userId: session.user.id,
      requestedAt: new Date().toISOString()
    };

    // Wyślij dane na webhook
    const webhookUrl = process.env.AUDIT_WEBHOOK_URL;

    if (!webhookUrl) {
      return NextResponse.json(
        {
          success: false,
          error: 'Audit webhook URL not configured'
        },
        { status: 500 }
      );
    }

    try {
      const webhookResponse = await fetch(webhookUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(webhookData),
      });

      if (!webhookResponse.ok) {
        throw new Error(`Webhook responded with status: ${webhookResponse.status}`);
      }

      // Zaktualizuj status domeny na "in_audit" i zwiększ liczbę audytów
      const currentDate = new Date();
      await Domain.findByIdAndUpdate(id, {
        status: 'in_audit',
        metadata: {
          ...domain.metadata,
          hasAudit: true,
          dateAudit: currentDate.toISOString()
        }
      });

      return NextResponse.json({
        success: true,
        message: 'Audit request sent successfully',
        data: {
          domainId: domain._id,
          cms: domain.cms,
          domain: domain.domain,
          status: 'in_audit'
        }
      });

    } catch (webhookError) {
      console.error('Error sending webhook:', webhookError);
      return NextResponse.json(
        {
          success: false,
          error: 'Failed to send audit request'
        },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Error executing audit:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error occurred while executing audit'
      },
      { status: 500 }
    );
  }
}
