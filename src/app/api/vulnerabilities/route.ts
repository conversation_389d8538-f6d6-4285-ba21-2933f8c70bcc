import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { Session } from 'next-auth';
import { authOptions } from '@/lib/auth';
import connectDB from '@/lib/db';
import Vulnerability from '@/models/Vulnerability';

export async function GET(request: NextRequest) {
  try {
    const session = (await getServerSession(authOptions)) as Session | null;

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search');

    await connectDB();

    // Build filter
    const filter: any = {};
    
    if (search) {
      filter.$or = [
        { vulnerability_id: { $regex: search, $options: 'i' } },
        { vulnerability_name: { $regex: search, $options: 'i' } },
        { volnerability_description: { $regex: search, $options: 'i' } }
      ];
    }

    // Get total count for pagination
    const totalCount = await Vulnerability.countDocuments(filter);

    // Get vulnerabilities with pagination
    const vulnerabilities = await Vulnerability.find(filter)
      .sort({ createdAt: -1 })
      .skip((page - 1) * limit)
      .limit(limit);

    return NextResponse.json({
      success: true,
      vulnerabilities: vulnerabilities.map(vulnerability => ({
        id: vulnerability._id,
        vulnerability_id: vulnerability.vulnerability_id,
        vulnerability_name: vulnerability.vulnerability_name,
        volnerability_description: vulnerability.volnerability_description,
        createdAt: vulnerability.createdAt,
        updatedAt: vulnerability.updatedAt
      })),
      pagination: {
        page,
        limit,
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
        hasNext: page * limit < totalCount,
        hasPrev: page > 1
      }
    });

  } catch (error) {
    console.error('Błąd pobierania podatności:', error);
    return NextResponse.json(
      { error: 'Wystąpił błąd podczas pobierania podatności' },
      { status: 500 }
    );
  }
}

async function generateVulnerabilityDescription(vulnerabilityName: string): Promise<string> {
  try {
    const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${process.env.OPENROUTER_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'google/gemini-flash-1.5',
        messages: [
          {
            role: 'user',
            content: `Opisz w dwóch zdaniach ale szczegółowo podatność ${vulnerabilityName} w taki sposób aby podatność wyglądała na poważną.`
          }
        ],
        max_tokens: 300,
        temperature: 0.7
      })
    });

    if (!response.ok) {
      throw new Error(`OpenRouter API error: ${response.status}`);
    }

    const data = await response.json();
    return data.choices?.[0]?.message?.content || 'Nie udało się wygenerować opisu podatności.';
  } catch (error) {
    console.error('Błąd generowania opisu przez AI:', error);
    return 'Poważna podatność bezpieczeństwa wymagająca natychmiastowej uwagi. Może prowadzić do kompromitacji systemu i utraty wrażliwych danych.';
  }
}

export async function POST(request: NextRequest) {
  try {
    const { vulnerability_id, vulnerability_name } = await request.json();

    if (!vulnerability_id || vulnerability_id.trim().length === 0) {
      return NextResponse.json(
        { error: 'ID podatności jest wymagane' },
        { status: 400 }
      );
    }

    if (!vulnerability_name || vulnerability_name.trim().length === 0) {
      return NextResponse.json(
        { error: 'Nazwa podatności jest wymagana' },
        { status: 400 }
      );
    }

    await connectDB();

    const existingVulnerability = await Vulnerability.findOne({
      vulnerability_id: vulnerability_id.trim()
    });

    if (existingVulnerability) {
      return NextResponse.json({
        success: true,
        vulnerability: {
          id: existingVulnerability._id,
          vulnerability_id: existingVulnerability.vulnerability_id,
          vulnerability_name: existingVulnerability.vulnerability_name,
          volnerability_description: existingVulnerability.volnerability_description,
          createdAt: existingVulnerability.createdAt,
          updatedAt: existingVulnerability.updatedAt
        }
      });
    }

    console.log(`Generowanie opisu dla podatności: ${vulnerability_name}`);
    const generatedDescription = await generateVulnerabilityDescription(vulnerability_name.trim());

    const vulnerability = new Vulnerability({
      vulnerability_id: vulnerability_id.trim(),
      vulnerability_name: vulnerability_name.trim(),
      volnerability_description: generatedDescription
    });

    await vulnerability.save();

    return NextResponse.json({
      success: true,
      vulnerability: {
        id: vulnerability._id,
        vulnerability_id: vulnerability.vulnerability_id,
        vulnerability_name: vulnerability.vulnerability_name,
        volnerability_description: vulnerability.volnerability_description,
        createdAt: vulnerability.createdAt,
        updatedAt: vulnerability.updatedAt
      }
    });

  } catch (error) {
    console.error('Błąd tworzenia podatności:', error);
    return NextResponse.json(
      { error: 'Wystąpił błąd podczas tworzenia podatności' },
      { status: 500 }
    );
  }
}