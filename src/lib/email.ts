import nodemailer from 'nodemailer';
import { sendTrackedEmail } from '@/lib/mail-tracking';

// SMTP configuration interface
interface SMTPConfig {
  host: string;
  port: number;
  secure: boolean;
  auth: {
    user: string;
    pass: string;
  };
}

// Email options interface
interface EmailOptions {
  to: string;
  subject: string;
  html: string;
  text?: string;
}

// Get SMTP configuration from environment variables
function getSMTPConfig(): SMTPConfig {
  const host = process.env.SMTP_HOST;
  const port = process.env.SMTP_PORT;
  const user = process.env.SMTP_USER;
  const pass = process.env.SMTP_PASS;

  if (!host || !port || !user || !pass) {
    throw new Error('SMTP configuration is incomplete. Please check environment variables: SMTP_HOST, SMTP_PORT, SMTP_USER, SMTP_PASS');
  }

  return {
    host,
    port: parseInt(port),
    secure: parseInt(port) === 465, // true for 465, false for other ports
    auth: {
      user,
      pass,
    },
  };
}

// Create transporter with SMTP configuration
function createTransporter() {
  const config = getSMTPConfig();
  return nodemailer.createTransport(config);
}

// Send email function
export async function sendEmail(options: EmailOptions): Promise<boolean> {
  try {
    const transporter = createTransporter();

    // Verify SMTP connection
    await transporter.verify();

    // Send email
    const info = await transporter.sendMail({
      from: process.env.SMTP_FROM || process.env.SMTP_USER,
      to: options.to,
      subject: options.subject,
      html: options.html,
      text: options.text || options.html.replace(/<[^>]*>/g, ''), // Strip HTML tags for text version
    });

    console.log('Email sent successfully:', info.messageId);
    return true;
  } catch (error) {
    console.error('Error sending email:', error);
    return false;
  }
}

// Helper function to decode JSON string if needed
function decodeAuditContent(content: string): string {
  if (!content) return "";

  try {
    // Try to parse as JSON - if it's a JSON string, decode it
    const parsed = JSON.parse(content);
    if (typeof parsed === "string") {
      return parsed;
    }
    // If it's not a string after parsing, return original
    return content;
  } catch (error) {
    console.log(error)
    // If JSON.parse fails, it's not a JSON string, return as is
    return content;
  }
}

// Generate HTML content for audit email
export function generateAuditEmailHTML(
  auditContent: string,
  domainName: string
): string {
  const htmlContent = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>Audyt Bezpieczeństwa - ${domainName}</title>
      <style>
         body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      color: #333333;
      background-color: #f9f9f9;
      margin: 0;
      padding: 0;
    }
    .container {
      max-width: 700px;
      margin: 20px auto;
      padding: 25px;
      background-color: #ffffff;
      border: 1px solid #dddddd;
      border-radius: 5px;
    }
    h1, h2, h3 , h4{
margin-top: 0;
      color: #222222;
    }
    h1 {
      font-size: 24px;

      border-bottom: 2px solid #eeeeee;
      padding-bottom: 10px;
      margin-bottom: 20px;
    }
    h2 {
      font-size: 20px;
      color: #c0392b; /* A concerning red color */
      margin-top: 30px;
    }
    h3 {
        font-size: 16px;
        margin-top: 25px;
        color: #34495e;
    }
    table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 20px;
    }
    th, td {
      border: 1px solid #dddddd;
      padding: 12px;
      text-align: left;
    }
    th {
      background-color: #f2f2f2;
      font-weight: bold;
    }
    .status-update {
      font-weight: bold;
      color: #e67e22; /* Orange for warning */
    }
    .status-ok {
      font-weight: bold;
      color: #27ae60; /* Green for OK */
    }
    .vulnerability {
        border-left: 4px solid #c0392b;
        margin-top: 15px;
    }
    .vulnerability-title {
        font-weight: bold;
        color: #c0392b;
    }
    .final-note {
        margin-top: 30px;
        padding: 20px;
        background-color: #eaf2f8;
        border-left: 4px solid #3498db;
    }
    strong.warning {
        color: #c0392b;
    }
    code {
      background-color: #ecf0f1;
      padding: 2px 5px;
      border-radius: 3px;
      font-family: monospace;
    }
      </style>
    </head>
    <body>

      <div class="content">
        <div style="width: 960px; max-width: 100%;">
          ${decodeAuditContent(auditContent)}
        </div>
      </div>
    </body>
    </html>
  `;

  return htmlContent;
}

// Send audit email specifically with tracking
export async function sendAuditEmail(
  recipientEmail: string,
  subject: string,
  auditContent: string,
  domainName: string,
  emailHistoryId?: string
): Promise<{ success: boolean; htmlContent: string }> {
  const htmlContent = generateAuditEmailHTML(auditContent, domainName);

  try {
    // Użyj trackowanego wysyłania maili
    const results = await sendTrackedEmail(
      recipientEmail,
      subject,
      htmlContent,
      emailHistoryId
    );

    // Sprawdź czy wysyłanie się powiodło
    const success = results.every(result => !result.error);

    return {
      success,
      htmlContent: htmlContent
    };
  } catch (error) {
    console.error('Error sending tracked audit email:', error);

    // Fallback do standardowego wysyłania
    const emailSent = await sendEmail({
      to: recipientEmail,
      subject,
      html: htmlContent,
    });

    return {
      success: emailSent,
      htmlContent: htmlContent
    };
  }
}

// Test SMTP connection
export async function testSMTPConnection(): Promise<boolean> {
  try {
    const transporter = createTransporter();
    await transporter.verify();
    console.log('SMTP connection test successful');
    return true;
  } catch (error) {
    console.error('SMTP connection test failed:', error);
    return false;
  }
}
