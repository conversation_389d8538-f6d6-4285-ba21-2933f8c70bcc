import json
import requests
import time
import os
import argparse
from functools import lru_cache
from pymongo import MongoClient
from datetime import datetime

@lru_cache(maxsize=100)
def get_plugin_info_from_wordpress_api(plugin_slug):
  """
  Pobiera informacje o pluginie z WordPress.org API.

  Args:
    plugin_slug (str): Slug plugina

  Returns:
    dict: Informacje o pluginie lub None w przypadku błędu
  """
  try:
    url = f"https://api.wordpress.org/plugins/info/1.0/{plugin_slug}.json"

    headers = {
      'User-Agent': 'Mozilla/5.0 (compatible; WPScan-Plugin-Info/1.0)'
    }

    response = requests.get(url, headers=headers, timeout=10)

    if response.status_code == 200:
      plugin_info = response.json()
      return {
        'name': plugin_info.get('name', plugin_slug),
        'short_description': plugin_info.get('short_description', ''),
        'author': plugin_info.get('author', ''),
        'homepage': plugin_info.get('homepage', ''),
        'download_link': plugin_info.get('download_link', ''),
        'active_installs': plugin_info.get('active_installs', 0),
        'tested': plugin_info.get('tested', ''),
        'requires': plugin_info.get('requires', ''),
        'rating': plugin_info.get('rating', 0),
        'num_ratings': plugin_info.get('num_ratings', 0)
      }
    elif response.status_code == 404:
      return None
    else:
      print(f"Błąd API WordPress.org dla {plugin_slug}: {response.status_code}")
      return None

  except requests.exceptions.RequestException as e:
    print(f"Błąd połączenia z WordPress.org API dla {plugin_slug}: {e}")
    return None
  except Exception as e:
    print(f"Nieoczekiwany błąd przy pobieraniu info o {plugin_slug}: {e}")
    return None

def get_vulnerability_description_from_api(vulnerability_id):
  """
  Pobiera szczegółowy opis podatności z API.

  Args:
    vulnerability_id (str): ID podatności

  Returns:
    str: Opis podatności lub None jeśli nie znaleziono
  """
  try:
    url = f"http://localhost:3000/api/public/vulnerability/{vulnerability_id}"
    headers = {
      'User-Agent': 'Mozilla/5.0 (compatible; WPScan-Vulnerability-Checker/1.0)'
    }
    
    response = requests.get(url, headers=headers, timeout=5)
    
    if response.status_code == 200:
      data = response.json()
      if data.get('success') and 'vulnerability' in data:
        return data['vulnerability'].get('volnerability_description')
    
    return None
    
  except Exception as e:
    print(f"Błąd podczas pobierania opisu podatności {vulnerability_id}: {e}")
    return None

def create_vulnerability_with_ai_description(vulnerability_id, vulnerability_name):
  """
  Tworzy nową podatność w bazie danych z opisem wygenerowanym przez AI.

  Args:
    vulnerability_id (str): ID podatności
    vulnerability_name (str): Nazwa podatności

  Returns:
    str: Wygenerowany opis podatności lub None w przypadku błędu
  """
  try:
    url = "http://localhost:3000/api/vulnerabilities"
    headers = {
      'Content-Type': 'application/json',
      'User-Agent': 'Mozilla/5.0 (compatible; WPScan-Vulnerability-Creator/1.0)'
    }
    
    data = {
      'vulnerability_id': vulnerability_id,
      'vulnerability_name': vulnerability_name
    }
    
    response = requests.post(url, headers=headers, json=data, timeout=30)
    
    if response.status_code == 200:
      response_data = response.json()
      if response_data.get('success') and 'vulnerability' in response_data:
        return response_data['vulnerability'].get('volnerability_description')
    
    return None
    
  except Exception as e:
    print(f"Błąd podczas tworzenia podatności {vulnerability_id}: {e}")
    return None

def check_plugin_vulnerabilities_in_database(plugin_slug, version=None, database_path="database.json", fetch_api_descriptions=True):
  """
  Sprawdza podatności dla plugina w lokalnej bazie Wordfence Intelligence API.

  Args:
    plugin_slug (str): Nazwa plugina
    version (str): Wersja plugina (opcjonalnie)
    database_path (str): Ścieżka do pliku database.json
    fetch_api_descriptions (bool): Czy pobierać opisy z API

  Returns:
    list: Lista podatności dla plugina lub pusta lista
  """
  if not os.path.exists(database_path):
    print(f"Plik bazy danych {database_path} nie istnieje.")
    return []

  try:
    with open(database_path, 'r', encoding='utf-8') as f:
      database = json.load(f)
  except json.JSONDecodeError as e:
    print(f"Błąd dekodowania JSON w bazie danych: {e}")
    return []
  except Exception as e:
    print(f"Błąd wczytywania bazy danych: {e}")
    return []

  vulnerabilities = []

  # Iteruj przez wszystkie podatności w bazie (klucze to UUID)
  for vuln_id, vuln_data in database.items():
    if not isinstance(vuln_data, dict):
      continue

    # Sprawdź czy podatność dotyczy naszego plugina
    software_list = vuln_data.get('software', [])
    if not isinstance(software_list, list):
      continue

    for software in software_list:
      if not isinstance(software, dict):
        continue

      # Sprawdź czy to ten plugin
      if software.get('slug') == plugin_slug and software.get('type') == 'plugin':
        # Pobierz szczegółowy opis z API tylko jeśli włączono
        api_description = None
        if fetch_api_descriptions:
          api_description = get_vulnerability_description_from_api(vuln_id)
        
        vuln_info = {
          'id': vuln_id,
          'title': vuln_data.get('title', 'Brak tytułu'),
          'description': api_description if api_description else vuln_data.get('description', 'Brak opisu'),
          'cvss_score': vuln_data.get('cvss', {}).get('score'),
          'cwe_id': vuln_data.get('cwe', {}).get('id'),
          'published': vuln_data.get('published'),
          'affected_versions': software.get('affected_versions', {}),
          'patched_versions': software.get('patched_versions', {})
        }

        # Jeśli podano wersję, sprawdź czy jest podatna
        if version:
          affected_versions = software.get('affected_versions', {})
          if is_version_affected(version, affected_versions):
            vulnerabilities.append(vuln_info)
        else:
          vulnerabilities.append(vuln_info)

  return vulnerabilities

def enhance_vulnerabilities_with_api_descriptions(scan_data):
  """
  Pobiera szczegółowe opisy podatności z API dla wszystkich podatności w danych skanowania.
  Jeśli podatność nie istnieje w bazie, tworzy ją z opisem wygenerowanym przez AI.
  
  Args:
    scan_data (dict): Dane skanowania z podatnościami
    
  Returns:
    dict: Dane skanowania z zaktualizowanymi opisami podatności
  """
  plugins = scan_data.get('plugins', {})
  
  for plugin_slug, plugin_info in plugins.items():
    vulnerabilities = plugin_info.get('vulnerabilities', [])
    
    for vulnerability in vulnerabilities:
      vuln_id = vulnerability.get('id')
      vuln_title = vulnerability.get('title', '')
      
      if vuln_id:
        print(f"Sprawdzanie podatności: {vuln_id}")
        
        # Najpierw spróbuj pobrać istniejący opis
        api_description = get_vulnerability_description_from_api(vuln_id)
        
        if api_description:
          vulnerability['description'] = api_description
          print(f"✓ Zaktualizowano opis podatności {vuln_id}")
        else:
          print(f"⚠ Nie znaleziono podatności {vuln_id} w bazie - tworzenie z AI...")
          
          # Spróbuj utworzyć podatność z opisem AI
          ai_description = create_vulnerability_with_ai_description(vuln_id, vuln_title)
          
          if ai_description:
            vulnerability['description'] = ai_description
            print(f"✓ Utworzono podatność {vuln_id} z opisem AI")
          else:
            print(f"✗ Nie udało się utworzyć podatności {vuln_id}")
        
        time.sleep(0.2)  # Pauza między zapytaniami
  
  return scan_data

def is_version_affected(version, affected_versions):
  """
  Sprawdza czy dana wersja jest podatna na podstawie informacji o podatnych wersjach.

  Args:
    version (str): Wersja do sprawdzenia
    affected_versions (dict): Słownik z informacjami o podatnych wersjach

  Returns:
    bool: True jeśli wersja jest podatna
  """
  if not isinstance(affected_versions, dict):
    return False

  # Sprawdź bezpośrednie dopasowanie wersji
  if version in affected_versions:
    return True

  # Sprawdź zakresy wersji (implementacja podstawowa)
  for affected_version in affected_versions.keys():
    if affected_version.startswith('<=') and version <= affected_version[2:].strip():
      return True
    elif affected_version.startswith('<') and version < affected_version[1:].strip():
      return True
    elif affected_version.startswith('>=') and version >= affected_version[2:].strip():
      return True
    elif affected_version.startswith('>') and version > affected_version[1:].strip():
      return True
    elif '-' in affected_version:
      # Zakres wersji np. "1.0-2.0"
      try:
        start_version, end_version = affected_version.split('-', 1)
        if start_version.strip() <= version <= end_version.strip():
          return True
      except:
        continue

  return False

def get_plugin_vulnerabilities(plugin_slug, version=None):
  """
  Pobiera podatności dla plugina z WPVulnerability.com API.

  Args:
    plugin_slug (str): Nazwa plugina
    version (str): Wersja plugina (opcjonalnie)

  Returns:
    dict: Dane o podatnościach lub None w przypadku błędu
  """
  try:
    url = f"https://www.wpvulnerability.com/api/plugins/{plugin_slug}"

    headers = {
      'User-Agent': 'Mozilla/5.0 (compatible; WPScan-Vulnerability-Checker/1.0)'
    }

    response = requests.get(url, headers=headers, timeout=10)

    if response.status_code == 200:
      vuln_data = response.json()

      if version and vuln_data:
        filtered_vulns = []
        for vuln in vuln_data:
          if 'affected_versions' in vuln:
            affected = vuln['affected_versions']
            if version in affected or f"<= {version}" in affected or any(version in av for av in affected):
              filtered_vulns.append(vuln)
        return filtered_vulns if filtered_vulns else None

      return vuln_data if vuln_data else None

    elif response.status_code == 404:
      return None
    else:
      print(f"Błąd API dla {plugin_slug}: {response.status_code}")
      return None

  except requests.exceptions.RequestException as e:
    print(f"Błąd połączenia dla {plugin_slug}: {e}")
    return None
  except Exception as e:
    print(f"Nieoczekiwany błąd dla {plugin_slug}: {e}")
    return None

def get_plugin_info_from_file(file_path, database_path="database.json"):
  """
  Wczytuje dane skanowania WPScan z pliku JSON i zwraca JSON z informacjami o pluginach,
  wraz z informacjami o tym, skąd pochodzą dane o wersji, konkretne linki oraz podatności.

  Args:
    file_path (str): Ścieżka do pliku JSON zawierającego dane wyjściowe skanowania WPScan.
    database_path (str): Ścieżka do pliku database.json z bazą podatności Wordfence.

  Returns:
    dict: Słownik z informacjami o pluginach i wersji WordPress, lub komunikat o błędzie.
  """
  try:
    with open(file_path, 'r', encoding='utf-8') as f:
      scan_data = json.load(f)
  except FileNotFoundError:
    return {"error": f"Plik '{file_path}' nie został znaleziony."}
  except json.JSONDecodeError:
    return {"error": f"Błąd dekodowania JSON w pliku '{file_path}'. Upewnij się, że plik jest poprawnym formatem JSON."}

  plugin_data = {}
  plugins = scan_data.get("plugins", {})

  # Wyciągnij informacje o użytkownikach
  users = scan_data.get("users", {})
  user_data = {}

  for username, user_info in users.items():
    user_data[username] = {
      "id": user_info.get("id")
    }

  # Wyciągnij informacje o wersji WordPress
  wp_version = scan_data.get("version", {})
  domain_url = scan_data.get("target_url")

  version_info = {
    "version_number": wp_version.get("number"),
    "version_status": wp_version.get("status"),
    "release_date": wp_version.get("release_date")
  }

  for slug, info in plugins.items():
    if slug != "*":
      version = info.get("version", {})
      latest_version = info.get("latest_version")
      outdated = info.get("outdated")

      # Pobieranie informacji o źródle danych z obsługą błędów
      try:
        found_by = version.get("found_by") if version else None
        confirmed_by_info = version.get("confirmed_by", {}) if version else {}
        confirmed_by_methods = list(confirmed_by_info.keys()) if confirmed_by_info else []

        source_info = found_by if found_by else "Nieznane"
        if confirmed_by_methods:
          source_info += " (potwierdzone przez: " + ", ".join(confirmed_by_methods) + ")"
      except AttributeError:
        # Jeśli version jest None, kontynuuj z domyślnymi wartościami
        found_by = None
        confirmed_by_info = {}
        confirmed_by_methods = []
        source_info = "Nieznane"

      # Pobieranie konkretnych linków z obsługą błędów
      source_links = []
      try:
        if version and version.get("interesting_entries"):
          source_links.extend(version.get("interesting_entries"))

        for method, method_info in confirmed_by_info.items():
            if method_info and method_info.get("interesting_entries"):
                source_links.extend(method_info.get("interesting_entries"))
      except AttributeError:
        # Jeśli wystąpi błąd, kontynuuj z pustą listą linków
        source_links = []

      # Usuwanie duplikatów linków i formatowanie
      source_links = sorted(list(set(source_links)))

      # Sprawdzenie podatności w lokalnej bazie danych z obsługą błędów
      try:
        installed_version = version.get("number") if version else None
      except AttributeError:
        installed_version = None

      vulnerabilities = check_plugin_vulnerabilities_in_database(slug, installed_version, database_path, fetch_api_descriptions=False)

      # Pobieranie informacji z WordPress.org API
      print(f"Pobieranie informacji o pluginie: {slug}")
      wp_info = get_plugin_info_from_wordpress_api(slug)
      time.sleep(0.5)  # Pauza między zapytaniami do API


      if installed_version != latest_version:

        plugin_info = {
            "name": "",
            "installed_version": installed_version,
            "latest_version": latest_version,
            "status": "Wymaga aktualizacji" if outdated else "Aktualny",
            "source": source_info,
            "source_links": source_links,
            "vulnerabilities_count": len(vulnerabilities),
            "vulnerabilities": vulnerabilities
        }

        # Dodaj informacje z WordPress.org API jeśli dostępne
        if wp_info:
          plugin_info.update({
            "name": wp_info.get('name', slug),
          })
        else:
          plugin_info.update({
            "name": slug,
          })

        plugin_data[slug] = plugin_info

  return {
    "domain_id": args.id,
    "domain_url": domain_url,
    "wordpress_version": version_info,
    "plugins": plugin_data,
    "users": user_data
  }

def save_to_mongodb(data, connection_string, db_name, collection_name):
  """
  Zapisuje dane do kolekcji MongoDB.

  Args:
    data (dict): Dane do zapisania zawierające informacje o WordPress i pluginach
    connection_string (str): String połączenia MongoDB
    db_name (str): Nazwa bazy danych
    collection_name (str): Nazwa kolekcji

  Returns:
    bool: True jeśli udało się zapisać, False w przeciwnym razie
  """
  try:
    client = MongoClient(connection_string)
    db = client[db_name]
    collection = db[collection_name]

    scan_document = {
      "timestamp": datetime.now(),
      "domain_id": data.get("domain_id"),
      "domain_url": data.get("domain_url"),
      "wordpress_version": data.get("wordpress_version", {}),
      "plugins": data.get("plugins", {}),
      "users": data.get("users", {})
    }

    result = collection.insert_one(scan_document)
    client.close()

    print(f"Dane zapisane do MongoDB z ID: {result.inserted_id}")
    return True

  except Exception as e:
    print(f"Błąd podczas zapisywania do MongoDB: {e}")
    return False

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="WPScan plugin vulnerability scanner")
    parser.add_argument("--id", type=str, help="ID of the scan file to load from ./scans/ID.json")
    args = parser.parse_args()

    if args.id:
        file_name = f"./scans/{args.id}.json"
        if not os.path.exists(file_name):
            print(f"BŁĄD: Plik '{file_name}' nie został znaleziony.")
            exit(1)
    else:
        file_name = "scan.json"

    database_name = f"./database.json"

    # Sprawdź czy plik bazy danych istnieje
    if not os.path.exists(database_name):
        print(f"UWAGA: Plik bazy danych '{database_name}' nie został znaleziony.")
        print("Pobierz bazę podatności Wordfence Intelligence API i zapisz jako 'database.json'")
        print("API URL: https://www.wordfence.com/api/intelligence/v2/vulnerabilities/production")

    scan_data = get_plugin_info_from_file(file_name, database_name)

    # Pobierz szczegółowe opisy podatności z API
    print("\n=== Pobieranie szczegółowych opisów podatności z API ===")
    enhanced_scan_data = enhance_vulnerabilities_with_api_descriptions(scan_data)
    print("=== Zakończono pobieranie opisów podatności ===\n")

    # Zapisz wynik do pliku _final.json
    with open(f"./scans/{args.id}_final.json", 'w', encoding='utf-8') as f:
        json.dump(enhanced_scan_data, f, indent=2, ensure_ascii=False)

    # Zapisz dane do MongoDB
    mongodb_connection = "mongodb+srv://fasolqa:<EMAIL>/?retryWrites=true&w=majority&appName=Studio"
    save_to_mongodb(enhanced_scan_data, mongodb_connection, "test", "scans")

    print(f"Wynik zapisano do pliku: ./scans/{args.id}_final.json")
    print("Dane także zapisano do MongoDB")
    print(json.dumps(enhanced_scan_data, indent=2, ensure_ascii=False))