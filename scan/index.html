<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WordPress Plugin Vulnerability Report</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
            border-radius: 10px;
        }

        h1 {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
        }

        .subtitle {
            text-align: center;
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .summary-card {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }

        .summary-card h3 {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }

        .summary-card.vulnerable h3 {
            color: #e74c3c;
        }

        .summary-card.secure h3 {
            color: #27ae60;
        }

        .summary-card.outdated h3 {
            color: #f39c12;
        }

        .filters {
            background: white;
            padding: 1rem;
            border-radius: 10px;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .filter-group {
            display: flex;
            gap: 1rem;
            align-items: center;
            flex-wrap: wrap;
        }

        .filter-group label {
            font-weight: 600;
        }

        .filter-group input, .filter-group select {
            padding: 0.5rem;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
        }

        .plugin-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
            gap: 1.5rem;
        }

        .plugin-card {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .plugin-card:hover {
            transform: translateY(-5px);
        }

        .plugin-header {
            padding: 1.5rem;
            border-bottom: 1px solid #eee;
        }

        .plugin-name {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: #2c3e50;
        }

        .plugin-version {
            display: flex;
            justify-content: space-between;
            margin-bottom: 1rem;
        }

        .version-info {
            font-size: 0.9rem;
            padding: 0.25rem 0.5rem;
            border-radius: 15px;
            font-weight: 500;
        }

        .current-version {
            background-color: #3498db;
            color: white;
        }

        .latest-version {
            background-color: #27ae60;
            color: white;
        }

        .status {
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.9rem;
            text-align: center;
        }

        .status.aktualny {
            background-color: #d4edda;
            color: #155724;
        }

        .status.wymaga-aktualizacji {
            background-color: #fff3cd;
            color: #856404;
        }

        .vulnerability-count {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            margin-top: 1rem;
            padding: 0.75rem;
            border-radius: 8px;
            font-weight: 600;
        }

        .vulnerability-count.none {
            background-color: #d4edda;
            color: #155724;
        }

        .vulnerability-count.has-vulns {
            background-color: #f8d7da;
            color: #721c24;
        }

        .vulnerabilities {
            padding: 0 1.5rem 1.5rem;
        }
domain_id
        .vulnerability-item {
            background-color: #f8f9fa;
            border-left: 4px solid #e74c3c;
            padding: 1rem;
            margin-bottom: 1rem;
            border-radius: 5px;
        }

        .vuln-title {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 0.5rem;
            font-size: 0.95rem;
        }

        .vuln-meta {
            display: flex;
            gap: 1rem;
            margin-bottom: 0.5rem;
            font-size: 0.85rem;
            color: #666;
        }

        .vuln-meta span {
            background: #e9ecef;
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
        }

        .cvss-score {
            font-weight: 600;
        }

        .cvss-high { color: #dc3545; }
        .cvss-medium { color: #fd7e14; }
        .cvss-low { color: #ffc107; }

        .hidden {
            display: none;
        }

        .loading {
            text-align: center;
            padding: 3rem;
            font-size: 1.2rem;
            color: #666;
        }

        .error {
            background-color: #f8d7da;
            color: #721c24;
            padding: 1rem;
            border-radius: 5px;
            text-align: center;
            margin: 2rem 0;
        }

        @media (max-width: 768px) {
            .plugin-grid {
                grid-template-columns: 1fr;
            }

            .summary {
                grid-template-columns: repeat(2, 1fr);
            }

            .filter-group {
                flex-direction: column;
                align-items: flex-start;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>WordPress Security Report</h1>
            <p class="subtitle">Plugin Vulnerability Analysis</p>
        </header>

        <div class="summary" id="summary">
            <div class="summary-card">
                <h3 id="totalPlugins">0</h3>
                <p>Total Plugins</p>
            </div>
            <div class="summary-card vulnerable">
                <h3 id="vulnerablePlugins">0</h3>
                <p>Vulnerable Plugins</p>
            </div>
            <div class="summary-card outdated">
                <h3 id="outdatedPlugins">0</h3>
                <p>Outdated Plugins</p>
            </div>
            <div class="summary-card vulnerable">
                <h3 id="totalVulns">0</h3>
                <p>Total Vulnerabilities</p>
            </div>
        </div>

        <div class="filters">
            <div class="filter-group">
                <label for="searchInput">Search:</label>
                <input type="text" id="searchInput" placeholder="Search plugins...">

                <label for="statusFilter">Status:</label>
                <select id="statusFilter">
                    <option value="all">All</option>
                    <option value="vulnerable">Vulnerable Only</option>
                    <option value="outdated">Outdated Only</option>
                    <option value="secure">Secure & Updated</option>
                </select>

                <label for="sortBy">Sort by:</label>
                <select id="sortBy">
                    <option value="name">Plugin Name</option>
                    <option value="vulnerabilities">Vulnerability Count</option>
                    <option value="status">Status</option>
                </select>
            </div>
        </div>

        <div id="loading" class="loading">
            Loading vulnerability data...
        </div>

        <div id="error" class="error hidden">
            Failed to load vulnerability data. Please make sure data.json file is accessible.
        </div>

        <div id="pluginGrid" class="plugin-grid">
            <!-- Plugin cards will be inserted here -->
        </div>
    </div>

    <script>
        let pluginData = {};
        let filteredData = {};

        // Load and parse the data.json file
        async function loadVulnerabilityData() {
            try {
                const response = await fetch('data.json');
                if (!response.ok) {
                    throw new Error('Failed to fetch data');
                }
                const data = await response.json();
                pluginData = data;
                filteredData = { ...data };

                document.getElementById('loading').classList.add('hidden');
                updateSummary();
                renderPlugins();
                setupEventListeners();
            } catch (error) {
                console.error('Error loading data:', error);
                document.getElementById('loading').classList.add('hidden');
                document.getElementById('error').classList.remove('hidden');
            }
        }

        // Update summary statistics
        function updateSummary() {
            const plugins = Object.values(filteredData);
            const totalPlugins = plugins.length;
            const vulnerablePlugins = plugins.filter(p => p.vulnerabilities_count > 0).length;
            const outdatedPlugins = plugins.filter(p => p.status === 'Wymaga aktualizacji').length;
            const totalVulns = plugins.reduce((sum, p) => sum + p.vulnerabilities_count, 0);

            document.getElementById('totalPlugins').textContent = totalPlugins;
            document.getElementById('vulnerablePlugins').textContent = vulnerablePlugins;
            document.getElementById('outdatedPlugins').textContent = outdatedPlugins;
            document.getElementById('totalVulns').textContent = totalVulns;
        }

        // Get CVSS severity class
        function getCVSSClass(score) {
            if (!score) return '';
            if (score >= 7.0) return 'cvss-high';
            if (score >= 4.0) return 'cvss-medium';
            return 'cvss-low';
        }

        // Format date
        function formatDate(dateString) {
            if (!dateString) return 'Unknown';
            return new Date(dateString).toLocaleDateString();
        }

        // Create vulnerability HTML
        function createVulnerabilityHTML(vulnerabilities) {
            if (vulnerabilities.length === 0) return '';

            return vulnerabilities.map(vuln => `
                <div class="vulnerability-item">
                    <div class="vuln-title">${vuln.title}</div>
                    <div class="vuln-meta">
                        ${vuln.cvss_score ? `<span class="cvss-score ${getCVSSClass(vuln.cvss_score)}">CVSS: ${vuln.cvss_score}</span>` : ''}
                        ${vuln.cwe_id ? `<span>CWE: ${vuln.cwe_id}</span>` : ''}
                        <span>Published: ${formatDate(vuln.published)}</span>
                    </div>
                    ${vuln.description && vuln.description !== 'Brak opisu' ? `<div class="vuln-description">${vuln.description}</div>` : ''}
                </div>
            `).join('');
        }

        // Create plugin card HTML
        function createPluginCard(slug, plugin) {
            const statusClass = plugin.status === 'Aktualny' ? 'aktualny' : 'wymaga-aktualizacji';
            const vulnCountClass = plugin.vulnerabilities_count === 0 ? 'none' : 'has-vulns';
            const vulnIcon = plugin.vulnerabilities_count === 0 ? '✅' : '⚠️';

            return `
                <div class="plugin-card" data-plugin="${slug}">
                    <div class="plugin-header">
                        <div class="plugin-name">${slug}</div>
                        <div class="plugin-version">
                            <span class="version-info current-version">Current: ${plugin.installed_version}</span>
                            <span class="version-info latest-version">Latest: ${plugin.latest_version}</span>
                        </div>
                        <div class="status ${statusClass}">${plugin.status}</div>
                        <div class="vulnerability-count ${vulnCountClass}">
                            ${vulnIcon} ${plugin.vulnerabilities_count} Vulnerability${plugin.vulnerabilities_count !== 1 ? 'ies' : ''}
                        </div>
                    </div>
                    ${plugin.vulnerabilities_count > 0 ? `
                    <div class="vulnerabilities">
                        ${createVulnerabilityHTML(plugin.vulnerabilities)}
                    </div>
                    ` : ''}
                </div>
            `;
        }

        // Render all plugins
        function renderPlugins() {
            const pluginGrid = document.getElementById('pluginGrid');
            const sortBy = document.getElementById('sortBy').value;

            // Sort plugins
            let sortedEntries = Object.entries(filteredData);

            switch(sortBy) {
                case 'vulnerabilities':
                    sortedEntries.sort((a, b) => b[1].vulnerabilities_count - a[1].vulnerabilities_count);
                    break;
                case 'status':
                    sortedEntries.sort((a, b) => {
                        if (a[1].status === b[1].status) return a[0].localeCompare(b[0]);
                        return a[1].status === 'Wymaga aktualizacji' ? -1 : 1;
                    });
                    break;
                default: // name
                    sortedEntries.sort((a, b) => a[0].localeCompare(b[0]));
            }

            pluginGrid.innerHTML = sortedEntries.map(([slug, plugin]) =>
                createPluginCard(slug, plugin)
            ).join('');
        }

        // Apply filters
        function applyFilters() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const statusFilter = document.getElementById('statusFilter').value;

            filteredData = {};

            Object.entries(pluginData).forEach(([slug, plugin]) => {
                // Search filter
                if (searchTerm && !slug.toLowerCase().includes(searchTerm)) {
                    return;
                }

                // Status filter
                switch(statusFilter) {
                    case 'vulnerable':
                        if (plugin.vulnerabilities_count === 0) return;
                        break;
                    case 'outdated':
                        if (plugin.status !== 'Wymaga aktualizacji') return;
                        break;
                    case 'secure':
                        if (plugin.vulnerabilities_count > 0 || plugin.status !== 'Aktualny') return;
                        break;
                }

                filteredData[slug] = plugin;
            });

            updateSummary();
            renderPlugins();
        }

        // Setup event listeners
        function setupEventListeners() {
            document.getElementById('searchInput').addEventListener('input', applyFilters);
            document.getElementById('statusFilter').addEventListener('change', applyFilters);
            document.getElementById('sortBy').addEventListener('change', renderPlugins);
        }

        // Load data when page loads
        document.addEventListener('DOMContentLoaded', loadVulnerabilityData);
    </script>
</body>
</html>